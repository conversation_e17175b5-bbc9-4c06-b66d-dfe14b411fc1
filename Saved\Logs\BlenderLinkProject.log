﻿Log file open, 06/01/25 13:12:46
LogWindows: Failed to load 'aqProf.dll' (GetLastError=126)
LogWindows: File 'aqProf.dll' does not exist
LogProfilingDebugging: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
LogWindows: Failed to load 'VtuneApi.dll' (GetLastError=126)
LogWindows: File 'VtuneApi.dll' does not exist
LogWindows: Failed to load 'VtuneApi32e.dll' (GetLastError=126)
LogWindows: File 'VtuneApi32e.dll' does not exist
LogWindows: Started CrashReportClient (pid=29396)
LogWindows: Custom abort handler registered for crash reporting.
LogInit: Display: Running engine for game: BlenderLinkProject
LogCore: UTS: Unreal Trace Server launched successfully
LogTrace: Initializing trace...
LogCore: Display: Requested channels: 'cpu,gpu,frame,log,bookmark,screenshot,region'
LogTrace: Display: Display Control listening on port 1985
LogTrace: Finished trace initialization.
LogCsvProfiler: Display: Metadata set : platform="Windows"
LogCsvProfiler: Display: Metadata set : config="Development"
LogCsvProfiler: Display: Metadata set : buildversion="++UE5+Release-5.5-***********"
LogCsvProfiler: Display: Metadata set : engineversion="5.5.4-40574608+++UE5+Release-5.5"
LogCsvProfiler: Display: Metadata set : os="Windows 11 (23H2) [10.0.22631.4751] "
LogCsvProfiler: Display: Metadata set : cpu="AuthenticAMD|AMD Ryzen 9 5950X 16-Core Processor"
LogCsvProfiler: Display: Metadata set : pgoenabled="0"
LogCsvProfiler: Display: Metadata set : pgoprofilingenabled="0"
LogCsvProfiler: Display: Metadata set : ltoenabled="0"
LogCsvProfiler: Display: Metadata set : asan="0"
LogCsvProfiler: Display: Metadata set : commandline="" H:\Plugins\BlenderLinkProject\BlenderLinkProject.uproject""
LogCsvProfiler: Display: Metadata set : loginid="5eff80b14364fb2f37e5468dbd2f7de6"
LogCsvProfiler: Display: Metadata set : llm="0"
LogStats: Stats thread started at 14.016746
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
LogICUInternationalization: ICU TimeZone Detection - Raw Offset: +5:30, Platform Override: ''
LogInit: Session CrashGUID >====================================================
         Session CrashGUID >   UECC-Windows-30380F094A46F3ACCA55A8803879F4B6
         Session CrashGUID >====================================================
LogConfig: No local boot hotfix file found at: [H:/Plugins/BlenderLinkProject/Saved/PersistentDownloadDir/HotfixForNextBoot.txt]
LogConfig: Display: Loading VulkanPC ini files took 0.03 seconds
LogConfig: Display: Loading Unix ini files took 0.03 seconds
LogConfig: Display: Loading Windows ini files took 0.03 seconds
LogConfig: Display: Loading Android ini files took 0.04 seconds
LogConfig: Display: Loading IOS ini files took 0.05 seconds
LogConfig: Display: Loading Linux ini files took 0.05 seconds
LogConfig: Display: Loading TVOS ini files took 0.05 seconds
LogConfig: Display: Loading VisionOS ini files took 0.05 seconds
LogConfig: Display: Loading LinuxArm64 ini files took 0.07 seconds
LogConfig: Display: Loading Mac ini files took 0.14 seconds
LogAudio: Display: Pre-Initializing Audio Device Manager...
LogAudio: Display: AudioInfo: 'OPUS' Registered
LogAudioDebug: Display: Lib vorbis DLL was dynamically loaded.
LogAudio: Display: AudioInfo: 'OGG' Registered
LogAudio: Display: AudioInfo: 'ADPCM' Registered
LogAudio: Display: AudioInfo: 'PCM' Registered
LogAudio: Display: AudioInfo: 'BINKA' Registered
LogAudio: Display: AudioInfo: 'RADA' Registered
LogAudio: Display: Audio Device Manager Pre-Initialized
LogPluginManager: Looking for build plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogPluginManager: Looking for enabled plugins target receipt
LogPluginManager: Found matching target receipt: H:/Plugins/BlenderLinkProject/Binaries/Win64/BlenderLinkProjectEditor.target
LogAssetRegistry: Display: Asset registry cache read as 44.1 MiB from H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_0.bin
LogPluginManager: Mounting Engine plugin Bridge
LogPluginManager: Mounting Engine plugin ChaosCloth
LogPluginManager: Mounting Engine plugin ChaosVD
LogPluginManager: Mounting Engine plugin CmdLinkServer
LogPluginManager: Mounting Engine plugin EnhancedInput
LogPluginManager: Mounting Engine plugin Fab
LogPluginManager: Mounting Engine plugin FastBuildController
LogPluginManager: Mounting Engine plugin MeshPainting
LogPluginManager: Mounting Engine plugin Mutable
LogPluginManager: Mounting Engine plugin RenderGraphInsights
LogPluginManager: Mounting Engine plugin TraceUtilities
LogPluginManager: Mounting Engine plugin UbaController
LogPluginManager: Mounting Engine plugin WorldMetrics
LogPluginManager: Mounting Engine plugin XGEController
LogPluginManager: Mounting Engine plugin AlembicImporter
LogPluginManager: Mounting Engine plugin MetaHuman
LogPluginManager: Mounting Engine plugin LauncherChunkInstaller
LogPluginManager: Mounting Engine plugin ActorLayerUtilities
LogPluginManager: Mounting Engine plugin AndroidFileServer
LogPluginManager: Mounting Engine plugin AndroidDeviceProfileSelector
LogPluginManager: Mounting Engine plugin AndroidMoviePlayer
LogPluginManager: Mounting Engine plugin AndroidPermission
LogPluginManager: Mounting Engine plugin AppleImageUtils
LogPluginManager: Mounting Engine plugin AppleMoviePlayer
LogPluginManager: Mounting Engine plugin AssetTags
LogPluginManager: Mounting Engine plugin ArchVisCharacter
LogPluginManager: Mounting Engine plugin AudioCapture
LogPluginManager: Mounting Engine plugin AudioWidgets
LogPluginManager: Mounting Engine plugin AudioSynesthesia
LogPluginManager: Mounting Engine plugin ChunkDownloader
LogPluginManager: Mounting Engine plugin CableComponent
LogPluginManager: Mounting Engine plugin ComputeFramework
LogPluginManager: Mounting Engine plugin CustomMeshComponent
LogPluginManager: Mounting Engine plugin ExampleDeviceProfileSelector
LogPluginManager: Mounting Engine plugin GeometryCache
LogPluginManager: Mounting Engine plugin GeometryProcessing
LogPluginManager: Mounting Engine plugin GoogleCloudMessaging
LogPluginManager: Mounting Engine plugin HairStrands
LogPluginManager: Mounting Engine plugin GooglePAD
LogPluginManager: Mounting Engine plugin IOSDeviceProfileSelector
LogPluginManager: Mounting Engine plugin InputDebugging
LogPluginManager: Mounting Engine plugin LinuxDeviceProfileSelector
LogPluginManager: Mounting Engine plugin LocationServicesBPLibrary
LogPluginManager: Mounting Engine plugin MeshModelingToolset
LogPluginManager: Mounting Engine plugin Metasound
LogPluginManager: Mounting Engine plugin MobilePatchingUtils
LogPluginManager: Mounting Engine plugin MsQuic
LogPluginManager: Mounting Engine plugin ProceduralMeshComponent
LogPluginManager: Mounting Engine plugin PropertyAccessEditor
LogPluginManager: Mounting Engine plugin RigVM
LogPluginManager: Mounting Engine plugin ResonanceAudio
LogPluginManager: Mounting Engine plugin SkeletalMerging
LogPluginManager: Mounting Engine plugin SoundFields
LogPluginManager: Mounting Engine plugin SignificanceManager
LogPluginManager: Mounting Engine plugin StateTree
LogPluginManager: Mounting Engine plugin Synthesis
LogPluginManager: Mounting Engine plugin USDCore
LogPluginManager: Mounting Engine plugin WaveTable
LogPluginManager: Mounting Engine plugin WebMMoviePlayer
LogPluginManager: Mounting Engine plugin WindowsDeviceProfileSelector
LogPluginManager: Mounting Engine plugin WindowsMoviePlayer
LogPluginManager: Mounting Engine plugin XRBase
LogPluginManager: Mounting Engine plugin CaptureData
LogPluginManager: Mounting Engine plugin CameraCalibrationCore
LogPluginManager: Mounting Engine plugin LensComponent
LogPluginManager: Mounting Engine plugin Takes
LogPluginManager: Mounting Engine plugin Paper2D
LogPluginManager: Mounting Engine plugin EnvironmentQueryEditor
LogPluginManager: Mounting Engine plugin AISupport
LogPluginManager: Mounting Engine plugin ACLPlugin
LogPluginManager: Mounting Engine plugin AnimationData
LogPluginManager: Mounting Engine plugin AnimationModifierLibrary
LogPluginManager: Mounting Engine plugin BlendSpaceMotionAnalysis
LogPluginManager: Mounting Engine plugin ControlRig
LogPluginManager: Mounting Engine plugin ControlRigSpline
LogPluginManager: Mounting Engine plugin DeformerGraph
LogPluginManager: Mounting Engine plugin IKRig
LogPluginManager: Mounting Engine plugin LiveLink
LogPluginManager: Mounting Engine plugin RigLogic
LogPluginManager: Mounting Engine plugin EngineCameras
LogPluginManager: Mounting Engine plugin GameplayCameras
LogPluginManager: Mounting Engine plugin CameraShakePreviewer
LogPluginManager: Mounting Engine plugin OpenColorIO
LogPluginManager: Mounting Engine plugin OodleNetwork
LogPluginManager: Mounting Engine plugin AnimationSharing
LogPluginManager: Mounting Engine plugin CLionSourceCodeAccess
LogPluginManager: Mounting Engine plugin CodeLiteSourceCodeAccess
LogPluginManager: Mounting Engine plugin DumpGPUServices
LogPluginManager: Mounting Engine plugin GitSourceControl
LogPluginManager: Mounting Engine plugin KDevelopSourceCodeAccess
LogPluginManager: Mounting Engine plugin N10XSourceCodeAccess
LogPluginManager: Mounting Engine plugin PerforceSourceControl
LogPluginManager: Mounting Engine plugin NullSourceCodeAccess
LogPluginManager: Mounting Engine plugin PixWinPlugin
LogPluginManager: Mounting Engine plugin PlasticSourceControl
LogPluginManager: Mounting Engine plugin PluginUtils
LogPluginManager: Mounting Engine plugin PropertyAccessNode
LogPluginManager: Mounting Engine plugin RenderDocPlugin
LogPluginManager: Mounting Engine plugin RiderSourceCodeAccess
LogPluginManager: Mounting Engine plugin SubversionSourceControl
LogPluginManager: Mounting Engine plugin TextureFormatOodle
LogPluginManager: Mounting Engine plugin UObjectPlugin
LogPluginManager: Mounting Engine plugin VisualStudioCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin VisualStudioSourceCodeAccess
LogPluginManager: Mounting Engine plugin XCodeSourceCodeAccess
LogPluginManager: Mounting Engine plugin AssetManagerEditor
LogPluginManager: Mounting Engine plugin BlueprintHeaderView
LogPluginManager: Mounting Engine plugin ChangelistReview
LogPluginManager: Mounting Engine plugin ColorGrading
LogPluginManager: Mounting Engine plugin CryptoKeys
LogPluginManager: Mounting Engine plugin CurveEditorTools
LogPluginManager: Mounting Engine plugin DataValidation
LogPluginManager: Mounting Engine plugin EditorDebugTools
LogPluginManager: Mounting Engine plugin GeometryMode
LogPluginManager: Mounting Engine plugin FacialAnimation
LogPluginManager: Mounting Engine plugin GameplayTagsEditor
LogPluginManager: Mounting Engine plugin EngineAssetDefinitions
LogPluginManager: Mounting Engine plugin EditorScriptingUtilities
LogPluginManager: Mounting Engine plugin MaterialAnalyzer
LogPluginManager: Mounting Engine plugin MacGraphicsSwitching
LogPluginManager: Mounting Engine plugin MeshLODToolset
LogPluginManager: Mounting Engine plugin MobileLauncherProfileWizard
LogPluginManager: Mounting Engine plugin ModelingToolsEditorMode
LogPluginManager: Mounting Engine plugin PluginBrowser
LogPluginManager: Mounting Engine plugin ProxyLODPlugin
LogPluginManager: Mounting Engine plugin SequencerAnimTools
LogPluginManager: Mounting Engine plugin SpeedTreeImporter
LogPluginManager: Mounting Engine plugin StylusInput
LogPluginManager: Mounting Engine plugin UVEditor
LogPluginManager: Mounting Engine plugin UMGWidgetPreview
LogPluginManager: Mounting Engine plugin WorldPartitionHLODUtilities
LogPluginManager: Mounting Engine plugin DatasmithContent
LogPluginManager: Mounting Engine plugin GLTFExporter
LogPluginManager: Mounting Engine plugin VariantManager
LogPluginManager: Mounting Engine plugin VariantManagerContent
LogPluginManager: Mounting Engine plugin AdvancedRenamer
LogPluginManager: Mounting Engine plugin AutomationUtils
LogPluginManager: Mounting Engine plugin BackChannel
LogPluginManager: Mounting Engine plugin ChaosCaching
LogPluginManager: Mounting Engine plugin ChaosEditor
LogPluginManager: Mounting Engine plugin ChaosNiagara
LogPluginManager: Mounting Engine plugin ChaosSolverPlugin
LogPluginManager: Mounting Engine plugin ChaosUserDataPT
LogPluginManager: Mounting Engine plugin CharacterAI
LogPluginManager: Mounting Engine plugin Dataflow
LogPluginManager: Mounting Engine plugin EditorDataStorage
LogPluginManager: Mounting Engine plugin EditorPerformance
LogPluginManager: Mounting Engine plugin EditorTelemetry
LogPluginManager: Mounting Engine plugin Fracture
LogPluginManager: Mounting Engine plugin FullBodyIK
LogPluginManager: Mounting Engine plugin GeometryCollectionPlugin
LogPluginManager: Mounting Engine plugin GeometryFlow
LogPluginManager: Mounting Engine plugin LiveLinkControlRig
LogPluginManager: Mounting Engine plugin LocalizableMessage
LogPluginManager: Mounting Engine plugin LowLevelNetTrace
LogPluginManager: Mounting Engine plugin MeshModelingToolsetExp
LogPluginManager: Mounting Engine plugin NFORDenoise
LogPluginManager: Mounting Engine plugin PlanarCut
LogPluginManager: Mounting Engine plugin PlatformCrypto
LogPluginManager: Mounting Engine plugin PythonScriptPlugin
LogPluginManager: Mounting Engine plugin RigLogicMutable
LogPluginManager: Mounting Engine plugin SkeletalReduction
LogPluginManager: Mounting Engine plugin StudioTelemetry
LogPluginManager: Mounting Engine plugin ToolPresets
LogPluginManager: Mounting Engine plugin Niagara
LogPluginManager: Mounting Engine plugin NiagaraSimCaching
LogPluginManager: Mounting Engine plugin InterchangeEditor
LogPluginManager: Mounting Engine plugin Interchange
LogPluginManager: Mounting Engine plugin AndroidMedia
LogPluginManager: Mounting Engine plugin AvfMedia
LogPluginManager: Mounting Engine plugin ImgMedia
LogPluginManager: Mounting Engine plugin MediaCompositing
LogPluginManager: Mounting Engine plugin MediaIOFramework
LogPluginManager: Mounting Engine plugin MediaPlate
LogPluginManager: Mounting Engine plugin MediaPlayerEditor
LogPluginManager: Mounting Engine plugin WebMMedia
LogPluginManager: Mounting Engine plugin WmfMedia
LogPluginManager: Mounting Engine plugin TcpMessaging
LogPluginManager: Mounting Engine plugin UdpMessaging
LogPluginManager: Mounting Engine plugin LevelSequenceEditor
LogPluginManager: Mounting Engine plugin ActorSequence
LogPluginManager: Mounting Engine plugin SequencerScripting
LogPluginManager: Mounting Engine plugin TemplateSequence
LogPluginManager: Mounting Engine plugin NNEDenoiser
LogPluginManager: Mounting Engine plugin NNERuntimeORT
LogPluginManager: Mounting Engine plugin EOSShared
LogPluginManager: Mounting Engine plugin OnlineBase
LogPluginManager: Mounting Engine plugin OnlineServices
LogPluginManager: Mounting Engine plugin OnlineSubsystem
LogPluginManager: Mounting Engine plugin OnlineSubsystemNull
LogPluginManager: Mounting Engine plugin OnlineSubsystemUtils
LogPluginManager: Mounting Engine plugin InterchangeTests
LogPluginManager: Mounting Engine plugin ControlRigModules
LogPluginManager: Mounting Engine plugin InterchangeAssets
LogPluginManager: Mounting Engine plugin RiderLink
LogPluginManager: Mounting Engine plugin SQLiteCore
LogPluginManager: Mounting Engine plugin LightMixer
LogPluginManager: Mounting Engine plugin ObjectMixer
LogPluginManager: Mounting Engine plugin SkeletalMeshModelingTools
LogPluginManager: Mounting Engine plugin ConcertMain
LogPluginManager: Mounting Engine plugin XInputDevice
LogPluginManager: Mounting Engine plugin ContentBrowserClassDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserAssetDataSource
LogPluginManager: Mounting Engine plugin ContentBrowserFileDataSource
LogPluginManager: Mounting Engine plugin PortableObjectFileDataSource
LogPluginManager: Mounting Engine plugin HoldoutComposite
LogPluginManager: Mounting Engine plugin MetaHumanSDK
LogPluginManager: Mounting Engine plugin BaseCharacterFXEditor
LogPluginManager: Mounting Engine plugin OnlineSubsystemIOS
LogPluginManager: Mounting Engine plugin OnlineSubsystemGooglePlay
LogPluginManager: Mounting Engine plugin ARUtilities
LogPluginManager: Mounting Engine plugin ConcertSyncClient
LogPluginManager: Mounting Engine plugin ConcertSyncCore
LogPluginManager: Mounting Engine plugin AppleARKit
LogPluginManager: Mounting Engine plugin AppleARKitFaceSupport
LogPluginManager: Mounting Project plugin BlenderLink
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
SourceControl: Revision control is disabled
LogWindows: Failed to load 'WinPixGpuCapturer.dll' (GetLastError=126)
LogWindows: File 'WinPixGpuCapturer.dll' does not exist
PixWinPlugin: PIX capture plugin failed to initialize! Check that the process is launched from PIX.
LogConfig: Applying CVar settings from Section [/Script/RenderDocPlugin.RenderDocPluginSettings] File [Engine]
RenderDocPlugin: Display: RenderDoc plugin will not be loaded. Use '-AttachRenderDoc' on the cmd line or enable 'renderdoc.AutoAttach' in the plugin settings.
LogNFORDenoise: NFORDenoise function starting up
LogStudioTelemetry: Display: Starting StudioTelemetry Module
LogStudioTelemetry: Started StudioTelemetry Session
LogEOSSDK: Initializing EOSSDK Version:1.17.0-39599718
LogInit: Using libcurl 8.4.0
LogInit:  - built for Windows
LogInit:  - supports SSL with OpenSSL/1.1.1t
LogInit:  - supports HTTP deflate (compression) using libz 1.3
LogInit:  - other features:
LogInit:      CURL_VERSION_SSL
LogInit:      CURL_VERSION_LIBZ
LogInit:      CURL_VERSION_IPV6
LogInit:      CURL_VERSION_ASYNCHDNS
LogInit:      CURL_VERSION_LARGEFILE
LogInit:      CURL_VERSION_HTTP2
LogInit:  CurlRequestOptions (configurable via config and command line):
LogInit:  - bVerifyPeer = true  - Libcurl will verify peer certificate
LogInit:  - bUseHttpProxy = false  - Libcurl will NOT use HTTP proxy
LogInit:  - bDontReuseConnections = false  - Libcurl will reuse connections
LogInit:  - MaxHostConnections = 16  - Libcurl will limit the number of connections to a host
LogInit:  - LocalHostAddr = Default
LogInit:  - BufferSize = 65536
LogInit: CreateHttpThread using FCurlMultiPollEventLoopHttpThread
LogInit: Creating http thread with maximum 2147483647 concurrent requests
LogInit: WinSock: version 1.1 (2.2), MaxSocks=32767, MaxUdp=65467
LogOnline: OSS: Created online subsystem instance for: NULL
LogOnline: OSS: TryLoadSubsystemAndSetDefault: Loaded subsystem for type [NULL]
LogInit: ExecutableName: UnrealEditor.exe
LogInit: Build: ++UE5+Release-5.5-***********
LogInit: Platform=WindowsEditor
LogInit: MachineId=5eff80b14364fb2f37e5468dbd2f7de6
LogInit: DeviceId=
LogInit: Engine Version: 5.5.4-40574608+++UE5+Release-5.5
LogInit: Compatible Engine Version: 5.5.0-37670630+++UE5+Release-5.5
LogInit: Net CL: 37670630
LogInit: OS: Windows 11 (23H2) [10.0.22631.4751] (), CPU: AMD Ryzen 9 5950X 16-Core Processor            , GPU: AMD Radeon RX 6900 XT
LogInit: Compiled (64-bit): Mar  7 2025 14:49:53
LogInit: Architecture: x64
LogInit: Compiled with Visual C++: 19.38.33130.00
LogInit: Build Configuration: Development
LogInit: Branch Name: ++UE5+Release-5.5
LogInit: Command Line: 
LogInit: Base Directory: D:/UE_5.5/Engine/Binaries/Win64/
LogInit: Allocator: Mimalloc
LogInit: Installed Engine Build: 1
LogInit: This binary is optimized with LTO: no, PGO: no, instrumented for PGO data collection: no
LogDevObjectVersion: Number of dev versions registered: 37
LogDevObjectVersion:   Dev-Blueprints (B0D832E4-1F89-4F0D-ACCF-7EB736FD4AA2): 10
LogDevObjectVersion:   Dev-Build (E1C64328-A22C-4D53-A36C-8E866417BD8C): 0
LogDevObjectVersion:   Dev-Core (375EC13C-06E4-48FB-B500-84F0262A717E): 4
LogDevObjectVersion:   Dev-Editor (E4B068ED-F494-42E9-A231-DA0B2E46BB41): 40
LogDevObjectVersion:   Dev-Framework (CFFC743F-43B0-4480-9391-14DF171D2073): 37
LogDevObjectVersion:   Dev-Mobile (B02B49B5-BB20-44E9-A304-32B752E40360): 3
LogDevObjectVersion:   Dev-Networking (A4E4105C-59A1-49B5-A7C5-40C4547EDFEE): 0
LogDevObjectVersion:   Dev-Online (39C831C9-5AE6-47DC-9A44-9C173E1C8E7C): 0
LogDevObjectVersion:   Dev-Physics (78F01B33-EBEA-4F98-B9B4-84EACCB95AA2): 20
LogDevObjectVersion:   Dev-Platform (6631380F-2D4D-43E0-8009-CF276956A95A): 0
LogDevObjectVersion:   Dev-Rendering (12F88B9F-8875-4AFC-A67C-D90C383ABD29): 49
LogDevObjectVersion:   Dev-Sequencer (7B5AE74C-D270-4C10-A958-57980B212A5A): 13
LogDevObjectVersion:   Dev-VR (D7296918-1DD6-4BDD-9DE2-64A83CC13884): 3
LogDevObjectVersion:   Dev-LoadTimes (C2A15278-BFE7-4AFE-6C17-90FF531DF755): 1
LogDevObjectVersion:   Private-Geometry (6EACA3D4-40EC-4CC1-B786-8BED09428FC5): 3
LogDevObjectVersion:   Dev-AnimPhys (29E575DD-E0A3-4627-9D10-D276232CDCEA): 17
LogDevObjectVersion:   Dev-Anim (AF43A65D-7FD3-4947-9873-3E8ED9C1BB05): 15
LogDevObjectVersion:   Dev-ReflectionCapture (6B266CEC-1EC7-4B8F-A30B-E4D90942FC07): 1
LogDevObjectVersion:   Dev-Automation (0DF73D61-A23F-47EA-B727-89E90C41499A): 1
LogDevObjectVersion:   FortniteMain (601D1886-AC64-4F84-AA16-D3DE0DEAC7D6): 170
LogDevObjectVersion:   FortniteValkyrie (8DBC2C5B-54A7-43E0-A768-FCBB7DA29060): 8
LogDevObjectVersion:   FortniteSeason (5B4C06B7-2463-4AF8-805B-BF70CDF5D0DD): 13
LogDevObjectVersion:   FortniteRelease (E7086368-6B23-4C58-8439-1B7016265E91): 15
LogDevObjectVersion:   Dev-Enterprise (9DFFBCD6-494F-0158-E221-12823C92A888): 10
LogDevObjectVersion:   Dev-Niagara (F2AED0AC-9AFE-416F-8664-AA7FFA26D6FC): 1
LogDevObjectVersion:   Dev-Destruction (174F1F0B-B4C6-45A5-B13F-2EE8D0FB917D): 10
LogDevObjectVersion:   Dev-Physics-Ext (35F94A83-E258-406C-A318-09F59610247C): 41
LogDevObjectVersion:   Dev-PhysicsMaterial-Chaos (B68FC16E-8B1B-42E2-B453-215C058844FE): 1
LogDevObjectVersion:   Dev-CineCamera (B2E18506-4273-CFC2-A54E-F4BB758BBA07): 1
LogDevObjectVersion:   Dev-VirtualProduction (64F58936-FD1B-42BA-BA96-7289D5D0FA4E): 1
LogDevObjectVersion:   UE5-Main (697DD581-E64F-41AB-AA4A-51ECBEB7B628): 119
LogDevObjectVersion:   UE5-Release (D89B5E42-24BD-4D46-8412-ACA8DF641779): 51
LogDevObjectVersion:   UE5-PrivateFrosty (59DA5D52-1232-4948-B878-597870B8E98B): 8
LogDevObjectVersion:   Dev-MediaFramework (6F0ED827-A609-4895-9C91-998D90180EA4): 2
LogDevObjectVersion:   Dev-NaniteResearch (30D58BE3-95EA-4282-A6E3-B159D8EBB06A): 1
LogDevObjectVersion:   Dev-ComputeFramework (6304A3E7-0059-4F59-8CFC-21BD7721FD4E): 0
LogDevObjectVersion:   LensFileVersion (8652A554-966A-466C-9FD7-1C6DD61B1ADB): 1
LogConfig: Branch 'EditorLayout' had been unloaded. Reloading on-demand took 0.78ms
LogConfig: Branch 'Bridge' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'ChaosCloth' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CmdLinkServer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FastBuildController' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MeshPainting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderGraphInsights' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'UbaController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldMetrics' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XGEController' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AlembicImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MetaHuman' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LauncherChunkInstaller' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ActorLayerUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidFileServer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AndroidDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidPermission' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleImageUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleMoviePlayer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetTags' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ArchVisCharacter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioCapture' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioWidgets' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AudioSynesthesia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChunkDownloader' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CableComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ComputeFramework' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CustomMeshComponent' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ExampleDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GeometryCache' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryProcessing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GoogleCloudMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GooglePAD' had been unloaded. Reloading on-demand took 0.13ms
LogConfig: Branch 'IOSDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'InputDebugging' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'LinuxDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'LocationServicesBPLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobilePatchingUtils' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'MsQuic' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProceduralMeshComponent' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PropertyAccessEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ResonanceAudio' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SkeletalMerging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SoundFields' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SignificanceManager' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WaveTable' had been unloaded. Reloading on-demand took 0.11ms
LogConfig: Branch 'WebMMoviePlayer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'WindowsDeviceProfileSelector' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'WindowsMoviePlayer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'XRBase' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CaptureData' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'EnvironmentQueryEditor' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AISupport' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ACLPlugin' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnimationData' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AnimationModifierLibrary' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlendSpaceMotionAnalysis' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigSpline' had been unloaded. Reloading on-demand took 0.12ms
LogConfig: Branch 'RigLogic' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'EngineCameras' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'CameraShakePreviewer' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'OodleNetwork' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'AnimationSharing' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'CLionSourceCodeAccess' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'CodeLiteSourceCodeAccess' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'DumpGPUServices' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'GitSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'KDevelopSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'N10XSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PerforceSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NullSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PixWinPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlasticSourceControl' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PropertyAccessNode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RenderDocPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SubversionSourceControl' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'TextureFormatOodle' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UObjectPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VisualStudioCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'VisualStudioSourceCodeAccess' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'XCodeSourceCodeAccess' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AssetManagerEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlueprintHeaderView' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChangelistReview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ColorGrading' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CryptoKeys' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CurveEditorTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'DataValidation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDebugTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GeometryMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'FacialAnimation' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'GameplayTagsEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EngineAssetDefinitions' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MaterialAnalyzer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MacGraphicsSwitching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshLODToolset' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MobileLauncherProfileWizard' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ModelingToolsEditorMode' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PluginBrowser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ProxyLODPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerAnimTools' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SpeedTreeImporter' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StylusInput' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UVEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UMGWidgetPreview' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'WorldPartitionHLODUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'VariantManager' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AdvancedRenamer' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AutomationUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BackChannel' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosCaching' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosNiagara' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosSolverPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ChaosUserDataPT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'CharacterAI' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'Dataflow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorDataStorage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorPerformance' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EditorTelemetry' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'Fracture' had been unloaded. Reloading on-demand took 0.10ms
LogConfig: Branch 'GeometryCollectionPlugin' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'GeometryFlow' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LiveLinkControlRig' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LocalizableMessage' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LowLevelNetTrace' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MeshModelingToolsetExp' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NFORDenoise' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlanarCut' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PlatformCrypto' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'PythonScriptPlugin' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RigLogicMutable' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalReduction' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'StudioTelemetry' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NiagaraSimCaching' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'InterchangeEditor' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AndroidMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AvfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ImgMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaCompositing' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlate' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'MediaPlayerEditor' had been unloaded. Reloading on-demand took 0.09ms
LogConfig: Branch 'WebMMedia' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'WmfMedia' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TcpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'UdpMessaging' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LevelSequenceEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ActorSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SequencerScripting' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'TemplateSequence' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNEDenoiser' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'NNERuntimeORT' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'EOSShared' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineBase' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineServices' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystem' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemNull' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemUtils' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'InterchangeTests' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ControlRigModules' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'RiderLink' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'SQLiteCore' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'LightMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ObjectMixer' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'SkeletalMeshModelingTools' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ConcertMain' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'XInputDevice' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserClassDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'ContentBrowserAssetDataSource' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ContentBrowserFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'PortableObjectFileDataSource' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'BaseCharacterFXEditor' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'OnlineSubsystemIOS' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'OnlineSubsystemGooglePlay' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ARUtilities' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'ConcertSyncClient' had been unloaded. Reloading on-demand took 0.08ms
LogConfig: Branch 'AppleARKit' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'AppleARKitFaceSupport' had been unloaded. Reloading on-demand took 0.07ms
LogConfig: Branch 'BlenderLink' had been unloaded. Reloading on-demand took 0.07ms
LogInit: Presizing for max 25165824 objects, including 0 objects not considered by GC.
LogStreaming: Display: AsyncLoading2 - Created: Event Driven Loader: false, Async Loading Thread: false, Async Post Load: false
LogStreaming: Display: AsyncLoading2 - Initialized
LogInit: Object subsystem initialized
LogConfig: Set CVar [[con.DebugEarlyDefault:1]]
LogConfig: CVar [[con.DebugLateDefault:1]] deferred - dummy variable created
LogConfig: CVar [[con.DebugLateCheat:1]] deferred - dummy variable created
LogConfig: CVar [[LogNamedEventFilters:Frame *]] deferred - dummy variable created
LogConfig: Set CVar [[r.setres:1280x720]]
LogConfig: CVar [[framepro.ScopeMinTimeMicroseconds:10]] deferred - dummy variable created
LogConfig: Set CVar [[fx.NiagaraAllowRuntimeScalabilityChanges:1]]
LogConfig: CVar [[QualityLevelMapping:high]] deferred - dummy variable created
LogConfig: CVar [[r.Occlusion.SingleRHIThreadStall:1]] deferred - dummy variable created
LogConfig: Set CVar [[r.Nanite.Streaming.ReservedResources:1]]
LogConfig: Set CVar [[r.Nanite.Streaming.AsyncCompute:0	; Temporary workaround for Nanite geometry corruption (FORT-805141)]]
LogConfig: CVar [[D3D12.Bindless.ResourceDescriptorHeapSize:32768]] deferred - dummy variable created
LogConfig: CVar [[D3D12.Bindless.SamplerDescriptorHeapSize:2048]] deferred - dummy variable created
LogConfig: Set CVar [[r.PSOPrecache.GlobalShaders:1]]
LogConfig: Set CVar [[r.DynamicRes.DynamicFrameTime:1]]
LogConfig: Set CVar [[r.VRS.EnableSoftware:1]]
LogConfig: Set CVar [[r.VRS.ContrastAdaptiveShading:1]]
[2025.06.01-07.42.55:449][  0]LogConfig: Set CVar [[r.VSync:0]]
[2025.06.01-07.42.55:449][  0]LogConfig: Set CVar [[r.RHICmdBypass:0]]
[2025.06.01-07.42.55:455][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererSettings] File [Engine]
[2025.06.01-07.42.55:455][  0]LogConfig: CVar [[VisualizeCalibrationColorMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor]] deferred - dummy variable created
[2025.06.01-07.42.55:455][  0]LogConfig: CVar [[VisualizeCalibrationGrayscaleMaterialPath:/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale]] deferred - dummy variable created
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.GPUCrashDebugging:0]]
[2025.06.01-07.42.55:455][  0]LogConfig: CVar [[MaxSkinBones:(Default=65536,PerPlatform=(("Mobile", 256)))]] deferred - dummy variable created
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.AllowStaticLighting:0]]
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.GenerateMeshDistanceFields:1]]
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.DynamicGlobalIlluminationMethod:1]]
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.ReflectionMethod:1]]
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.SkinCache.CompileShaders:1]]
[2025.06.01-07.42.55:455][  0]LogConfig: Set CVar [[r.RayTracing:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.Shadow.Virtual.Enable:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.HighlightContrastScale:0.8]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.DefaultFeature.LocalExposure.ShadowContrastScale:0.8]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.GPUSkin.Support16BitBoneIndex:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.GPUSkin.UnlimitedBoneInfluences:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[SkeletalMesh.UseExperimentalChunking:1]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.Shaders.RemoveUnusedInterpolators:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[r.Shadow.DetectVertexShaderLayerAtRuntime:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.RendererOverrideSettings] File [Engine]
[2025.06.01-07.42.55:475][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.StreamingSettings] File [Engine]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.MinBulkDataSizeForAsyncLoading:131072]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.AsyncLoadingThreadEnabled:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.EventDrivenLoaderEnabled:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.WarnIfTimeLimitExceeded:0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.TimeLimitExceededMultiplier:1.5]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.TimeLimitExceededMinTime:0.005]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.UseBackgroundLevelStreaming:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.PriorityAsyncLoadingExtraTime:15.0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.LevelStreamingActorsUpdateTimeLimit:5.0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.PriorityLevelStreamingActorsUpdateExtraTime:5.0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsRegistrationGranularity:10]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.UnregisterComponentsTimeLimit:1.0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.LevelStreamingComponentsUnregistrationGranularity:5]]
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[s.MaxPackageSummarySize:16384]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[s.FlushStreamingOnExit:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__SoundBase]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__MaterialInterface]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[FixedBootOrder:/Script/Engine/Default__DeviceProfileManager]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.GarbageCollectionSettings] File [Engine]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.MaxObjectsNotConsideredByGC:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.FlushStreamingOnGC:0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.NumRetriesBeforeForcingGC:10]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.AllowParallelGC:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.TimeBetweenPurgingPendingKillObjects:61.1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.MaxObjectsInEditor:25165824]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.IncrementalBeginDestroyEnabled:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.CreateGCClusters:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.MinGCClusterSize:5]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.AssetClustreringEnabled:0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.ActorClusteringEnabled:0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.VerifyUObjectsAreNotFGCObjects:0]]
[2025.06.01-07.42.55:475][  0]LogConfig: Set CVar [[gc.GarbageEliminationEnabled:1]]
[2025.06.01-07.42.55:475][  0]LogConfig: Applying CVar settings from Section [/Script/Engine.NetworkSettings] File [Engine]
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Average",ToolTip="Simulates average internet conditions")]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[NetworkEmulationProfiles:(ProfileName="Bad",ToolTip="Simulates laggy internet conditions")]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: Applying CVar settings from Section [/Script/UnrealEd.CookerSettings] File [Engine]
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[DefaultASTCQualityBySpeed:2]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[DefaultASTCQualityBySize:3]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[DefaultASTCQualityBySizeHQ:4]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:WidgetBlueprint]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GroupActor]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MetaData]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ObjectRedirector]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NavMeshRenderingComponent]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ReflectionCaptureComponent]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:TextRenderComponent]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:Font]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:MaterialExpression]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraEmitter]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:NiagaraScript]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleEmitter]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleLODLevel]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:ParticleModule]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SubUVAnimation]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:SoundNode]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:GameplayEffectUIData]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedServer:AmbientSound]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:WidgetBlueprint]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:GroupActor]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:MetaData]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:ObjectRedirector]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[ClassesExcludedOnDedicatedClient:InterpCurveEdSetup]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.AllowStaticLighting]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.MaterialEditor.LWCTruncateMode]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.GBuffer]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.VelocityOutputPass]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.SelectiveBasePassOutputs]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.DBuffer]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Mobile.DBuffer]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Symbols]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.GenerateSymbols]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.WriteSymbols]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.AllowUniqueSymbols]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.ExtraData]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.Shaders.Optimize]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.CompileShadersForDevelopment]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.MobileHDR]] deferred - dummy variable created
[2025.06.01-07.42.55:475][  0]LogConfig: CVar [[VersionedIntRValues:r.UsePreExposure]] deferred - dummy variable created
[2025.06.01-07.42.55:551][  0]LogConfig: Applying CVar settings from Section [ViewDistanceQuality@3] File [Scalability]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.SkeletalMeshLODBias:0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.ViewDistanceScale:1.0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Applying CVar settings from Section [AntiAliasingQuality@3] File [Scalability]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.FXAA.Quality:4]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TemporalAA.Quality:2]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.History.R11G11B10:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.History.ScreenPercentage:200]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.History.UpdateQuality:3]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.ShadingRejection.Flickering:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.RejectionAntiAliasingQuality:2]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.ReprojectionField:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.TSR.Resurrection:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Applying CVar settings from Section [ShadowQuality@3] File [Scalability]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.LightFunctionQuality:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.ShadowQuality:5]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.CSM.MaxCascades:10]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.MaxResolution:2048]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.MaxCSMResolution:2048]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.RadiusThreshold:0.01]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.DistanceScale:1.0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.CSM.TransitionScale:1.0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.PreShadowResolutionFactor:1.0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.DistanceFieldShadowing:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.VolumetricFog:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.VolumetricFog.GridPixelSize:8]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.VolumetricFog.GridSizeZ:128]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.VolumetricFog.HistoryMissSupersampleCount:4]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.LightMaxDrawDistanceScale:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.CapsuleShadows:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.MaxPhysicalPages:4096]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectional:-1.5]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasDirectionalMoving:-1.5]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocal:0.0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.ResolutionLodBiasLocalMoving:1.0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountDirectional:8]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayDirectional:4]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.RayCountLocal:8]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Shadow.Virtual.SMRT.SamplesPerRayLocal:4]]
[2025.06.01-07.42.55:551][  0]LogConfig: Applying CVar settings from Section [GlobalIlluminationQuality@3] File [Scalability]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.DistanceFieldAO:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.AOQuality:2]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.DiffuseIndirect.Allow:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.ProbeSpacing:4]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.LumenScene.Radiosity.HemisphereProbeResolution:4]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.TraceMeshSDFs.Allow:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.ProbeResolution:32]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.RadianceCache.NumProbesToTraceBudget:300]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.DownsampleFactor:16]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TracingOctahedronResolution:8]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.IrradianceFormat:0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.StochasticInterpolation:0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.FullResolutionJitterWidth:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.TwoSidedFoliageBackfaceDiffuse:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ScreenTraces.HZBTraversal.FullResDepth:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.ShortRangeAO.HardwareRayTracing:0]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.GridPixelSize:32]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TraceFromVolume:1]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.TracingOctahedronResolution:3]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.ProbeResolution:8]]
[2025.06.01-07.42.55:551][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyVolume.RadianceCache.NumProbesToTraceBudget:200]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyLight.RealTimeReflectionCapture:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.RayTracing.Scene.BuildMode:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [ReflectionQuality@3] File [Scalability]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSR.Quality:3]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSR.HalfResSceneColor:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.Allow:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.DownsampleFactor:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.MaxRoughnessToTraceForFoliage:0.4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.ScreenProbeGather.MaxRoughnessToEvaluateRoughSpecularForFoliage:0.8]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.TonemapMode:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.Reflections.ScreenSpaceReconstruction.MinWeight:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Allow:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Lumen.TranslucencyReflections.FrontLayer.Enable:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [PostProcessQuality@3] File [Scalability]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.MotionBlurQuality:4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.MotionBlur.HalfResGather:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionMipLevelFactor:0.4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionMaxQuality:100]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionLevels:-1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.AmbientOcclusionRadiusScale:1.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DepthOfFieldQuality:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.RenderTargetPoolMin:400]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.LensFlareQuality:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SceneColorFringeQuality:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.EyeAdaptationQuality:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.BloomQuality:5]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Bloom.ScreenPercentage:50.000]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.FastBlurThreshold:100]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Upscale.Quality:3]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.LightShaftQuality:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Filter.SizeScale:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Tonemapper.Quality:5]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Gather.ResolutionDivisor:2         ; lower gathering resolution]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Gather.AccumulatorQuality:1        ; higher gathering accumulator quality]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Gather.PostfilterMethod:1          ; Median3x3 postfilering method]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Gather.EnableBokehSettings:0       ; no bokeh simulation when gathering]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Gather.RingCount:4                 ; medium number of samples when gathering]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.ForegroundCompositing:1    ; additive foreground scattering]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.BackgroundCompositing:2    ; additive background scattering]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.EnableBokehSettings:1      ; bokeh simulation when scattering]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Scatter.MaxSpriteRatio:0.1         ; only a maximum of 10% of scattered bokeh]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Recombine.Quality:1                ; cheap slight out of focus]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Recombine.EnableBokehSettings:0    ; no bokeh simulation on slight out of focus]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.TemporalAAQuality:1                ; more stable temporal accumulation]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxForegroundRadius:0.025]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DOF.Kernel.MaxBackgroundRadius:0.025]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [TextureQuality@3] File [Scalability]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.MipBias:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.AmortizeCPUToGPUCopy:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.MaxNumTexturesToStreamPerFrame:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.Boost:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.MaxAnisotropy:8]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.VT.MaxAnisotropy:8]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.LimitPoolSizeToVRAM:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.PoolSize:1000]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Streaming.MaxEffectiveScreenSize:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [EffectsQuality@3] File [Scalability]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.TranslucencyLightingVolumeDim:64]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.RefractionQuality:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SceneColorFormat:4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.DetailMode:3]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.TranslucencyVolumeBlur:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.MaterialQualityLevel:1 ; High quality]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSS.Scale:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSS.SampleSet:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSS.Quality:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSS.HalfRes:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SSGI.Quality:3]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.EmitterSpawnRateScale:1.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.ParticleLightQuality:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.FastApplyOnOpaque:1 ; Always have FastSkyLUT 1 in this case to avoid wrong sky]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.SampleCountMaxPerSlice:4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.AerialPerspectiveLUT.DepthResolution:16.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMin:4.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.FastSkyLUT.SampleCountMax:128.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMin:4.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.SampleCountMax:128.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.UseSmallFormat:0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.TransmittanceLUT.SampleCount:10.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.SkyAtmosphere.MultiScatteringLUT.SampleCount:15.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[fx.Niagara.QualityLevel:3]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.Refraction.OffsetQuality:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.DownsampleFactor:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.MaxStepCount:512]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.HeterogeneousVolumes.Shadows.Resolution:512]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [FoliageQuality@3] File [Scalability]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[foliage.DensityScale:1.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[grass.DensityScale:1.0]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [ShadingQuality@3] File [Scalability]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.HairStrands.SkyLighting.IntegrationType:2]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.HairStrands.SkyAO.SampleCount:4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.HairStrands.Visibility.MSAA.SamplePerPixel:4]]
[2025.06.01-07.42.55:554][  0]LogConfig: Set CVar [[r.AnisotropicMaterials:1]]
[2025.06.01-07.42.55:554][  0]LogConfig: Applying CVar settings from Section [LandscapeQuality@3] File [Scalability]
[2025.06.01-07.42.55:608][  0]LogRHI: Using Default RHI: D3D12
[2025.06.01-07.42.55:608][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.01-07.42.55:608][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.01-07.42.55:666][  0]LogD3D12RHI: Loading WinPixEventRuntime.dll for PIX profiling (from ../../../Engine/Binaries/ThirdParty/Windows/WinPixEventRuntime/x64).
[2025.06.01-07.42.55:666][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.01-07.42.56:115][  0]LogD3D12RHI: Found D3D12 adapter 0: AMD Radeon RX 6900 XT (VendorId: 1002, DeviceId: 73af, SubSysId: e3a1002, Revision: 00c0
[2025.06.01-07.42.56:115][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.01-07.42.56:115][  0]LogD3D12RHI:   Adapter has 16338MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 3 output[s]
[2025.06.01-07.42.56:128][  0]LogD3D12RHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.01-07.42.56:128][  0]LogD3D12RHI:      Driver Date: 4-25-2025
[2025.06.01-07.42.56:293][  0]LogD3D12RHI: Found D3D12 adapter 1: NVIDIA GeForce RTX 2080 Ti (VendorId: 10de, DeviceId: 1e07, SubSysId: 37151462, Revision: 00a1
[2025.06.01-07.42.56:293][  0]LogD3D12RHI:   Max supported Feature Level 12_2, shader model 6.7, binding tier 3, wave ops supported, atomic64 supported
[2025.06.01-07.42.56:293][  0]LogD3D12RHI:   Adapter has 11027MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.06.01-07.42.56:294][  0]LogD3D12RHI:   Driver Version: 576.52 (internal:32.0.15.7652, unified:576.52)
[2025.06.01-07.42.56:294][  0]LogD3D12RHI:      Driver Date: 5-14-2025
[2025.06.01-07.42.56:313][  0]LogD3D12RHI: Found D3D12 adapter 2: Microsoft Basic Render Driver (VendorId: 1414, DeviceId: 008c, SubSysId: 0000, Revision: 0000
[2025.06.01-07.42.56:313][  0]LogD3D12RHI:   Max supported Feature Level 12_1, shader model 6.2, binding tier 3, wave ops supported, atomic64 unsupported
[2025.06.01-07.42.56:313][  0]LogD3D12RHI:   Adapter has 0MB of dedicated video memory, 0MB of dedicated system memory, and 32726MB of shared system memory, 0 output[s]
[2025.06.01-07.42.56:314][  0]LogD3D12RHI: DirectX Agility SDK runtime found.
[2025.06.01-07.42.56:314][  0]LogD3D12RHI: Chosen D3D12 Adapter Id = 0
[2025.06.01-07.42.56:315][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.01-07.42.56:316][  0]LogInit: Selected Device Profile: [WindowsEditor]
[2025.06.01-07.42.56:330][  0]LogHAL: Display: Platform has ~ 64 GB [68632862720 / 68719476736 / 64], which maps to Largest [LargestMinGB=32, LargerMinGB=12, DefaultMinGB=8, SmallerMinGB=6, SmallestMinGB=0)
[2025.06.01-07.42.56:346][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [Windows]
[2025.06.01-07.42.56:357][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.01-07.42.56:365][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.RasterizationMode:Bitmap -> Msdf]]
[2025.06.01-07.42.56:365][  0]LogDeviceProfileManager: Pushing Device Profile CVar: [[UI.SlateSDFText.ResolutionLevel:2 -> 2]]
[2025.06.01-07.42.56:365][  0]LogConfig: Applying CVar settings from Section [Startup] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.01-07.42.56:386][  0]LogConfig: Set CVar [[r.DumpShaderDebugInfo:2]]
[2025.06.01-07.42.56:386][  0]LogConfig: Set CVar [[p.chaos.AllowCreatePhysxBodies:1]]
[2025.06.01-07.42.56:386][  0]LogConfig: Set CVar [[fx.SkipVectorVMBackendOptimizations:1]]
[2025.06.01-07.42.56:386][  0]LogConfig: CVar [[ds.CADTranslator.Meshing.ActivateThinZoneMeshing:0]] deferred - dummy variable created
[2025.06.01-07.42.56:386][  0]LogConfig: CVar [[ds.CADTranslator.Stitching.RemoveThinFaces:0]] deferred - dummy variable created
[2025.06.01-07.42.56:386][  0]LogConfig: Applying CVar settings from Section [Startup_Windows] File [../../../Engine/Config/ConsoleVariables.ini]
[2025.06.01-07.42.56:386][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [Engine]
[2025.06.01-07.42.56:386][  0]LogConfig: Set CVar [[memory.MemoryPressureCriticalThresholdMB:512]]
[2025.06.01-07.42.56:386][  0]LogConfig: Applying CVar settings from Section [ConsoleVariables] File [H:/Plugins/BlenderLinkProject/Saved/Config/WindowsEditor/Editor.ini]
[2025.06.01-07.42.56:408][  0]LogInit: Computer: DESKTOP-E41IK6R
[2025.06.01-07.42.56:408][  0]LogInit: User: Shashank
[2025.06.01-07.42.56:408][  0]LogInit: CPU Page size=4096, Cores=16
[2025.06.01-07.42.56:408][  0]LogInit: High frequency timer resolution =10.000000 MHz
[2025.06.01-07.42.56:675][  0]LogMemory: Memory total: Physical=63.9GB (64GB approx) Virtual=67.9GB
[2025.06.01-07.42.56:675][  0]LogMemory: Platform Memory Stats for WindowsEditor
[2025.06.01-07.42.56:675][  0]LogMemory: Process Physical Memory: 630.79 MB used, 663.61 MB peak
[2025.06.01-07.42.56:675][  0]LogMemory: Process Virtual Memory: 760.94 MB used, 760.94 MB peak
[2025.06.01-07.42.56:675][  0]LogMemory: Physical Memory: 20853.99 MB used,  44599.41 MB free, 65453.40 MB total
[2025.06.01-07.42.56:675][  0]LogMemory: Virtual Memory: 32434.58 MB used,  37114.82 MB free, 69549.40 MB total
[2025.06.01-07.42.56:675][  0]LogCsvProfiler: Display: Metadata set : extradevelopmentmemorymb="0"
[2025.06.01-07.42.56:679][  0]LogWindows: WindowsPlatformFeatures enabled
[2025.06.01-07.42.56:852][  0]LogChaosDD: Chaos Debug Draw Startup
[2025.06.01-07.42.56:883][  0]LogInit: Physics initialised using underlying interface: Chaos
[2025.06.01-07.42.56:916][  0]LogInit: Using OS detected language (en-GB).
[2025.06.01-07.42.56:916][  0]LogInit: Using OS detected locale (en-IN).
[2025.06.01-07.42.57:185][  0]LogTextLocalizationManager: No specific localization for 'en-GB' exists, so 'en' will be used for the language.
[2025.06.01-07.42.57:185][  0]LogInit: Setting process to per monitor DPI aware
[2025.06.01-07.42.57:977][  0]LogWindowsTextInputMethodSystem: Available input methods:
[2025.06.01-07.42.57:977][  0]LogWindowsTextInputMethodSystem:   - English (United States) - (Keyboard).
[2025.06.01-07.42.57:977][  0]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.01-07.42.58:329][  0]LogSlate: New Slate User Created. Platform User Id 0, User Index 0, Is Virtual User: 0
[2025.06.01-07.42.58:329][  0]LogSlate: Slate User Registered.  User Index 0, Is Virtual User: 0
[2025.06.01-07.42.59:967][  0]LogRHI: Using Default RHI: D3D12
[2025.06.01-07.42.59:967][  0]LogRHI: Using Highest Feature Level of D3D12: SM6
[2025.06.01-07.42.59:967][  0]LogRHI: Loading RHI module D3D12RHI
[2025.06.01-07.42.59:967][  0]LogRHI: Checking if RHI D3D12 with Feature Level SM6 is supported by your system.
[2025.06.01-07.42.59:967][  0]LogRHI: RHI D3D12 with Feature Level SM6 is supported and will be used.
[2025.06.01-07.42.59:967][  0]LogD3D12RHI: Display: Creating D3D12 RHI with Max Feature Level SM6
[2025.06.01-07.42.59:979][  0]LogWindows: Attached monitors:
[2025.06.01-07.42.59:979][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.06.01-07.42.59:979][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.06.01-07.42.59:979][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.06.01-07.42.59:979][  0]LogWindows: Found 3 attached monitors.
[2025.06.01-07.42.59:979][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.01-07.42.59:980][  0]LogRHI: RHI Adapter Info:
[2025.06.01-07.42.59:980][  0]LogRHI:             Name: AMD Radeon RX 6900 XT
[2025.06.01-07.42.59:980][  0]LogRHI:   Driver Version: AMD Software: Adrenalin Edition 25.5.1 (internal:32.0.21001.9024, unified:32.0.21001.9024)
[2025.06.01-07.42.59:980][  0]LogRHI:      Driver Date: 4-25-2025
[2025.06.01-07.42.59:980][  0]LogD3D12RHI:     GPU DeviceId: 0x73af (for the marketing name, search the web for "GPU Device Id")
[2025.06.01-07.43.00:019][  0]LogD3D12RHI: InitD3DDevice: -D3DDebug = off -D3D12GPUValidation = off
[2025.06.01-07.43.00:225][  0]LogNvidiaAftermath: Aftermath initialized
[2025.06.01-07.43.00:225][  0]LogD3D12RHI: Emitting draw events for PIX profiling.
[2025.06.01-07.43.00:331][  0]LogNvidiaAftermath: Warning: Skipping aftermath initialization on non-Nvidia device.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device1 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device2 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device3 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device4 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device5 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device6 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device7 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device8 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device9 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device10 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device11 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: ID3D12Device12 is supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: Bindless resources are supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: Stencil ref from pixel shader is supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: Raster order views are supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: Wave Operations are supported (wave size: min=32 max=64).
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: D3D12 ray tracing tier 1.1 and bindless resources are supported.
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: Mesh shader tier 1.0 is supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: AtomicInt64OnTypedResource is supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: AtomicInt64OnGroupShared is supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: AtomicInt64OnDescriptorHeapResource is supported
[2025.06.01-07.43.00:331][  0]LogD3D12RHI: Shader Model 6.6 atomic64 is supported
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000081E1C4E5300)
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000081E1C4E5580)
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: [GPUBreadCrumb] Successfully setup breadcrumb resource for DiagnosticBuffer (Queue: 0x0000081E1C4E5800)
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: Display: Not using pipeline state disk cache per r.D3D12.PSO.DiskCache=0
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: Display: Not using driver-optimized pipeline state disk cache per r.D3D12.PSO.DriverOptimizedDiskCache=0
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: AMD hit token extension is not supported
[2025.06.01-07.43.00:463][  0]LogRHI: Texture pool is 9809 MB (70% of 14013 MB)
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: Async texture creation enabled
[2025.06.01-07.43.00:463][  0]LogD3D12RHI: RHI has support for 64 bit atomics
[2025.06.01-07.43.00:845][  0]LogVRS: Current RHI supports per-draw and screenspace Variable Rate Shading
[2025.06.01-07.43.01:183][  0]LogInit: Initializing FReadOnlyCVARCache
[2025.06.01-07.43.01:259][  0]LogTurnkeySupport: Running Turnkey SDK detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all'
[2025.06.01-07.43.01:259][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_0.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_0.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -platform=all" ]
[2025.06.01-07.43.01:512][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatASTC
[2025.06.01-07.43.01:512][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatDXT
[2025.06.01-07.43.01:512][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatETC2
[2025.06.01-07.43.01:512][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatIntelISPCTexComp
[2025.06.01-07.43.01:512][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatUncompressed
[2025.06.01-07.43.01:512][  0]LogTextureFormatOodle: Display: Oodle Texture TFO init; latest sdk version = 2.9.12
[2025.06.01-07.43.01:512][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.12.dll
[2025.06.01-07.43.01:539][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.5.dll
[2025.06.01-07.43.01:565][  0]LogTextureFormatManager: Display: Loaded Base TextureFormat: TextureFormatOodle
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTC'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXT'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'AndroidClient'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ASTCClient'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_DXTClient'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_ETC2Client'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_Multi'
[2025.06.01-07.43.02:817][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Android_MultiClient'
[2025.06.01-07.43.03:018][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOS'
[2025.06.01-07.43.03:018][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'IOSClient'
[2025.06.01-07.43.03:178][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Linux'
[2025.06.01-07.43.03:178][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxEditor'
[2025.06.01-07.43.03:178][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxServer'
[2025.06.01-07.43.03:178][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxClient'
[2025.06.01-07.43.03:321][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64'
[2025.06.01-07.43.03:321][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Server'
[2025.06.01-07.43.03:321][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'LinuxArm64Client'
[2025.06.01-07.43.03:471][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Mac'
[2025.06.01-07.43.03:471][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacEditor'
[2025.06.01-07.43.03:471][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacServer'
[2025.06.01-07.43.03:471][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'MacClient'
[2025.06.01-07.43.03:653][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOS'
[2025.06.01-07.43.03:653][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'TVOSClient'
[2025.06.01-07.43.03:845][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'Windows'
[2025.06.01-07.43.03:845][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsEditor'
[2025.06.01-07.43.03:845][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsServer'
[2025.06.01-07.43.03:845][  0]LogTargetPlatformManager: Display: Loaded TargetPlatform 'WindowsClient'
[2025.06.01-07.43.03:845][  0]LogTargetPlatformManager: Display: Building Assets For WindowsEditor
[2025.06.01-07.43.06:057][  0]LogTargetPlatformManager: Unable to find shader format SF_METAL from hinted modules, loading all potential format modules to find it
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager: Loaded format module MetalShaderFormat
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_MRT
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_TVOS
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_MRT_TVOS
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_SM5
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_SM6
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_SIM
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_MACES3_1
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_METAL_MRT_MAC
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager: Loaded format module ShaderFormatD3D
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   PCD3D_SM6
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   PCD3D_SM5
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   PCD3D_ES31
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager: Loaded format module ShaderFormatOpenGL
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   GLSL_150_ES31
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   GLSL_ES3_1_ANDROID
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager: Loaded format module ShaderFormatVectorVM
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   VVM_1_0
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager: Loaded format module VulkanShaderFormat
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_VULKAN_SM5
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_VULKAN_ES31_ANDROID
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_VULKAN_ES31
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_VULKAN_SM5_ANDROID
[2025.06.01-07.43.06:087][  0]LogTargetPlatformManager:   SF_VULKAN_SM6
[2025.06.01-07.43.06:101][  0]LogRendererCore: Ray tracing is enabled (dynamic). Reason: r.RayTracing=1 and r.RayTracing.EnableOnDemand=1.
[2025.06.01-07.43.06:101][  0]LogRendererCore: Ray tracing shaders are enabled.
[2025.06.01-07.43.06:174][  0]LogDerivedDataCache: Display: Memory: Max Cache Size: -1 MB
[2025.06.01-07.43.06:174][  0]LogDerivedDataCache: FDerivedDataBackendGraph: Pak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/DDC.ddp not found, will not use a pak cache.
[2025.06.01-07.43.06:174][  0]LogDerivedDataCache: Unable to find inner node Pak for hierarchy Hierarchy.
[2025.06.01-07.43.06:174][  0]LogDerivedDataCache: FDerivedDataBackendGraph: CompressedPak pak cache file H:/Plugins/BlenderLinkProject/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.01-07.43.06:174][  0]LogDerivedDataCache: Unable to find inner node CompressedPak for hierarchy Hierarchy.
[2025.06.01-07.43.06:432][  0]LogDerivedDataCache: Display: ../../../Engine/DerivedDataCache/Compressed.ddp: Opened pak cache for reading. (1559 MiB)
[2025.06.01-07.43.06:432][  0]LogDerivedDataCache: FDerivedDataBackendGraph: EnterprisePak pak cache file ../../../Enterprise/DerivedDataCache/Compressed.ddp not found, will not use a pak cache.
[2025.06.01-07.43.06:432][  0]LogDerivedDataCache: Unable to find inner node EnterprisePak for hierarchy Hierarchy.
[2025.06.01-07.43.06:473][  0]LogZenServiceInstance: Found Zen config default=C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data
[2025.06.01-07.43.06:513][  0]LogZenServiceInstance: InTree version at 'D:/UE_5.5/Engine/Binaries/Win64/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.01-07.43.06:513][  0]LogZenServiceInstance: Installed version at 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe' is '5.5.7-202409112143-windows-x64-release-f523a01'
[2025.06.01-07.43.06:530][  0]LogZenServiceInstance: No current process using the data dir found, launching a new instance
[2025.06.01-07.43.06:531][  0]LogZenServiceInstance: Display: Launching executable 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install/zenserver.exe', working dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Install', data dir 'C:/Users/<USER>/AppData/Local/UnrealEngine/Common/Zen/Data', args '--port 8558 --data-dir "C:\Users\<USER>\AppData\Local\UnrealEngine\Common\Zen\Data" --http asio --gc-cache-duration-seconds 1209600 --gc-interval-seconds 21600 --gc-low-diskspace-threshold 2147483648 --quiet --http-forceloopback --owner-pid 27344 --child-id Zen_27344_Startup'
[2025.06.01-07.43.06:756][  0]LogZenServiceInstance: Display: Unreal Zen Storage Server HTTP service at [::1]:8558 status: OK!.
[2025.06.01-07.43.06:756][  0]LogZenServiceInstance: Local ZenServer AutoLaunch initialization completed in 0.283 seconds
[2025.06.01-07.43.06:908][  0]LogDerivedDataCache: Display: ZenLocal: Using ZenServer HTTP service at http://[::1]:8558/ with namespace ue.ddc status: OK!.
[2025.06.01-07.43.06:930][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Speed tests took 0.01 seconds.
[2025.06.01-07.43.06:933][  0]LogDerivedDataCache: Display: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Performance: Latency=0.09ms. RandomReadSpeed=232.82MBs, RandomWriteSpeed=318.57MBs. Assigned SpeedClass 'Local'
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: Local: Using data cache path C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: DeleteOnly
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: ZenShared: Disabled because Host is set to 'None'
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: Unable to find inner node ZenShared for hierarchy Hierarchy.
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: Shared: Disabled because no path is configured.
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: Unable to find inner node Shared for hierarchy Hierarchy.
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: Cloud: Disabled because Host is set to 'None'
[2025.06.01-07.43.06:934][  0]LogDerivedDataCache: Unable to find inner node Cloud for hierarchy Hierarchy.
[2025.06.01-07.43.07:073][  0]LogShaderCompilers: Guid format shader working directory is 19 characters bigger than the processId version (H:/Plugins/BlenderLinkProject/Intermediate/Shaders/WorkingDirectory/27344/).
[2025.06.01-07.43.07:073][  0]LogShaderCompilers: Cleaned the shader compiler working directory 'C:/Users/<USER>/AppData/Local/Temp/UnrealShaderWorkingDir/3BEA235A47FDCA5E1B6B33A42AC67B6E/'.
[2025.06.01-07.43.07:083][  0]LogXGEController: Cannot use XGE Controller as Incredibuild is not installed on this machine.
[2025.06.01-07.43.07:083][  0]LogShaderCompilers: Display: Using Local Shader Compiler with 16 workers.
[2025.06.01-07.43.07:135][  0]LogShaderCompilers: Display: Compiling shader autogen file: H:/Plugins/BlenderLinkProject/Intermediate/ShaderAutogen/PCD3D_SM6/AutogenShaderHeaders.ush
[2025.06.01-07.43.07:136][  0]LogShaderCompilers: Display: Autogen file is unchanged, skipping write.
[2025.06.01-07.43.20:641][  0]LogTurnkeySupport: Completed SDK detection: ExitCode = 0
[2025.06.01-07.43.23:760][  0]LogSlate: Using FreeType 2.10.0
[2025.06.01-07.43.23:868][  0]LogSlate: SlateFontServices - WITH_FREETYPE: 1, WITH_HARFBUZZ: 1
[2025.06.01-07.43.23:890][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.01-07.43.23:890][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.01-07.43.24:195][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.01-07.43.24:195][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.01-07.43.24:195][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.01-07.43.24:195][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.01-07.43.24:548][  0]LogAssetRegistry: FAssetRegistry took 0.0034 seconds to start up
[2025.06.01-07.43.24:792][  0]LogAssetRegistry: Display: AssetDataGatherer spent 0.000s loading caches H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin.
[2025.06.01-07.43.24:886][  0]LogAssetRegistry: Error: Package is unloadable: H:/Plugins/BlenderLinkProject/Content/SM_Rock_ukxmdhuga.uasset. Reason: Invalid value for PACKAGE_FILE_TAG at start of file.
[2025.06.01-07.43.25:401][  0]LogEditorDomain: Display: EditorDomain is Disabled
[2025.06.01-07.43.30:228][  0]LogStreaming: Display: FlushAsyncLoading(1): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.43.30:513][  0]LogTextureEncodingSettings: Display: Texture Encode Speed: FinalIfAvailable (editor).
[2025.06.01-07.43.30:513][  0]LogTextureEncodingSettings: Display: Oodle Texture Encode Speed settings: Fast: RDO Off Lambda=0, Effort=Normal Final: RDO Off Lambda=0, Effort=Normal
[2025.06.01-07.43.30:513][  0]LogTextureEncodingSettings: Display: Shared linear texture encoding: Disabled
[2025.06.01-07.43.30:524][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64Editor not found.
[2025.06.01-07.43.30:524][  0]LogDeviceProfileManager: Display: Deviceprofile LinuxArm64 not found.
[2025.06.01-07.43.30:563][  0]LogDeviceProfileManager: Active device profile: [0000081E39F2CE00][0000081E37FE0000 66] WindowsEditor
[2025.06.01-07.43.30:563][  0]LogCsvProfiler: Display: Metadata set : deviceprofile="WindowsEditor"
[2025.06.01-07.43.30:742][  0]LogTurnkeySupport: Turnkey Platform: Win64: (Status=Valid, MinAllowed_Sdk=10.0.19041.0, MaxAllowed_Sdk=10.9.99999.0, Current_Sdk=10.0.22621.0, Allowed_AutoSdk=10.0.22621.0, Current_AutoSdk=, Flags="InstalledSdk_ValidVersionExists, Sdk_HasBestVersion")
[2025.06.01-07.43.30:769][  0]LogTurnkeySupport: Running Turnkey device detection: ' -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R'
[2025.06.01-07.43.30:769][  0]LogMonitoredProcess: Running Serialized UAT: [ cmd.exe /c ""D:/UE_5.5/Engine/Build/BatchFiles/RunUAT.bat"  -ScriptsForProject="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject" Turnkey -utf8output -WaitForUATMutex -command=VerifySdk -ReportFilename="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyReport_1.log" -log="H:/Plugins/BlenderLinkProject/Intermediate/TurnkeyLog_1.log" -project="H:/Plugins/BlenderLinkProject/BlenderLinkProject.uproject"  -Device=Win64@DESKTOP-E41IK6R" -nocompile -nocompileuat ]
[2025.06.01-07.43.31:462][  0]LogTurnkeySupport: Completed device detection: Code = 0
[2025.06.01-07.43.32:791][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:846][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 18 to allow recursive sync load to finish
[2025.06.01-07.43.32:847][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.01-07.43.32:847][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:847][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 19 to allow recursive sync load to finish
[2025.06.01-07.43.32:847][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.01-07.43.32:847][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:848][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 20 to allow recursive sync load to finish
[2025.06.01-07.43.32:848][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.01-07.43.32:848][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:848][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 21 to allow recursive sync load to finish
[2025.06.01-07.43.32:848][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.01-07.43.32:854][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:877][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:877][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 23 to allow recursive sync load to finish
[2025.06.01-07.43.32:877][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.01-07.43.32:877][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:877][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 24 to allow recursive sync load to finish
[2025.06.01-07.43.32:877][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.01-07.43.32:878][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:894][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:894][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 26 to allow recursive sync load to finish
[2025.06.01-07.43.32:894][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.01-07.43.32:894][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:894][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 27 to allow recursive sync load to finish
[2025.06.01-07.43.32:894][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.01-07.43.32:895][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: WaitingForIo) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDiffuse with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness has reached state ExportsDone > CreateLinkerLoadExports, releasing request 29 to allow recursive sync load to finish
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/PowerToRoughness with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Flushing package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec has reached state ExportsDone > CreateLinkerLoadExports, releasing request 30 to allow recursive sync load to finish
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions01/Shading/ConvertFromDiffSpec with requester package /Engine/EngineMaterials/DefaultPostProcessMaterial
[2025.06.01-07.43.32:921][  0]LogStreaming: Display: Merging postload groups of package /Engine/Functions/Engine_MaterialFunctions02/Utility/BreakOutFloat2Components with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 28 to allow recursive sync load to finish
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultPostProcessMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultPostProcessMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 31 to allow recursive sync load to finish
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultPostProcessMaterial with requester package /Engine/EngineMaterials/DefaultLightFunctionMaterial
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 25 to allow recursive sync load to finish
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultLightFunctionMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultLightFunctionMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 32 to allow recursive sync load to finish
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultLightFunctionMaterial with requester package /Engine/EngineMaterials/DefaultDeferredDecalMaterial
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 22 to allow recursive sync load to finish
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Flushing package /Engine/EngineMaterials/DefaultDeferredDecalMaterial (state: ExportsDone) recursively from another package /Engine/EngineMaterials/WorldGridMaterial (state: CreateLinkerLoadExports) will result in a partially loaded package to avoid a deadlock.
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Package /Engine/EngineMaterials/DefaultDeferredDecalMaterial has reached state ExportsDone > CreateLinkerLoadExports, releasing request 33 to allow recursive sync load to finish
[2025.06.01-07.43.32:922][  0]LogStreaming: Display: Merging postload groups of package /Engine/EngineMaterials/DefaultDeferredDecalMaterial with requester package /Engine/EngineMaterials/WorldGridMaterial
[2025.06.01-07.43.34:204][  0]LogMeshReduction: Display: Using QuadricMeshReduction for automatic static mesh reduction
[2025.06.01-07.43.34:204][  0]LogMeshReduction: Display: Using SkeletalMeshReduction for automatic skeletal mesh reduction
[2025.06.01-07.43.34:204][  0]LogMeshReduction: Display: Using ProxyLODMeshReduction for automatic mesh merging
[2025.06.01-07.43.34:204][  0]LogMeshReduction: Display: No distributed automatic mesh merging module available
[2025.06.01-07.43.34:204][  0]LogMeshMerging: No distributed automatic mesh merging module available
[2025.06.01-07.43.38:974][  0]LogConfig: Branch 'PIEPreviewSettings' had been unloaded. Reloading on-demand took 0.57ms
[2025.06.01-07.43.39:635][  0]LogConfig: Branch 'GameplayTagsList' had been unloaded. Reloading on-demand took 0.50ms
[2025.06.01-07.43.40:303][  0]LogConfig: Branch 'TemplateDefs' had been unloaded. Reloading on-demand took 0.50ms
[2025.06.01-07.43.40:305][  0]LogConfig: Branch 'TemplateCategories' had been unloaded. Reloading on-demand took 0.48ms
[2025.06.01-07.43.45:326][  0]LogVirtualization: Display: VirtualizationSystem name found in ini file: None
[2025.06.01-07.43.45:327][  0]LogVirtualization: Display: FNullVirtualizationSystem mounted, virtualization will be disabled
[2025.06.01-07.43.45:424][  0]LogLiveCoding: Display: Starting LiveCoding
[2025.06.01-07.43.45:424][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: UnrealEditor Win64 Development
[2025.06.01-07.43.45:425][  0]LogLiveCoding: Display: First instance in process group "UE_BlenderLinkProject_0xe99fe6f9", spawning console
[2025.06.01-07.43.45:496][  0]LogLiveCoding: Display: Waiting for server
[2025.06.01-07.43.47:051][  0]LogSlate: Border
[2025.06.01-07.43.47:051][  0]LogSlate: BreadcrumbButton
[2025.06.01-07.43.47:051][  0]LogSlate: Brushes.Title
[2025.06.01-07.43.47:051][  0]LogSlate: Default
[2025.06.01-07.43.47:051][  0]LogSlate: Icons.Save
[2025.06.01-07.43.47:051][  0]LogSlate: Icons.Toolbar.Settings
[2025.06.01-07.43.47:051][  0]LogSlate: ListView
[2025.06.01-07.43.47:051][  0]LogSlate: SoftwareCursor_CardinalCross
[2025.06.01-07.43.47:051][  0]LogSlate: SoftwareCursor_Grab
[2025.06.01-07.43.47:051][  0]LogSlate: TableView.DarkRow
[2025.06.01-07.43.47:051][  0]LogSlate: TableView.Row
[2025.06.01-07.43.47:051][  0]LogSlate: TreeView
[2025.06.01-07.43.53:268][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize started...
[2025.06.01-07.43.53:732][  0]LogWorldPartition: Display: FWorldPartitionClassDescRegistry::Initialize took 463.635 ms
[2025.06.01-07.43.53:900][  0]LogConfig: Branch 'Mass' had been unloaded. Reloading on-demand took 0.53ms
[2025.06.01-07.43.54:227][  0]LogInit: XR: Instanced Stereo Rendering is Disabled
[2025.06.01-07.43.54:227][  0]LogInit: XR: MultiViewport is Disabled
[2025.06.01-07.43.54:227][  0]LogInit: XR: Mobile Multiview is Disabled
[2025.06.01-07.43.54:227][  0]LogTurnkeySupport: Turnkey Device: Win64@DESKTOP-E41IK6R: (Name=DESKTOP-E41IK6R, Type=Computer, Status=Valid, MinAllowed=10.0.19041.0, MaxAllowed=, Current=10.0.22631.0, Flags="Device_InstallSoftwareValid")
[2025.06.01-07.43.55:182][  0]LogLiveCoding: Display: Successfully initialized, removing startup thread
[2025.06.01-07.43.58:371][  0]LogMetaSound: Display: MetaSound Page Target Initialized to 'Default'
[2025.06.01-07.43.58:382][  0]LogAudio: Display: Registering Engine Module Parameter Interfaces...
[2025.06.01-07.43.58:452][  0]LogMetaSound: MetaSound Engine Initialized
[2025.06.01-07.44.00:737][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.06.01-07.44.00:737][  0]LogOpenColorIOEditor: Display: Force-disable invalid viewport transform settings.
[2025.06.01-07.44.01:369][  0]LogConfig: Branch 'TranslationPickerSettings' had been unloaded. Reloading on-demand took 0.70ms
[2025.06.01-07.44.02:972][  0]LogNiagaraDebuggerClient: Niagara Debugger Client Initialized | Session: B7EB4D08984C4E398000000000004E00 | Instance: A59F193645C7CAB17B3E6F83A8BB3359 (DESKTOP-E41IK6R-27344).
[2025.06.01-07.44.08:299][  0]LogTcpMessaging: Initializing TcpMessaging bridge
[2025.06.01-07.44.08:329][  0]LogUdpMessaging: Display: Work queue size set to 1024.
[2025.06.01-07.44.08:330][  0]LogUdpMessaging: Initializing bridge on interface 0.0.0.0:0 to multicast group 230.0.0.1:6666.
[2025.06.01-07.44.08:330][  0]LogUdpMessaging: Display: Unicast socket bound to '0.0.0.0:56110'.
[2025.06.01-07.44.08:333][  0]LogUdpMessaging: Display: Added local interface '192.168.1.12' to multicast group '230.0.0.1:6666'
[2025.06.01-07.44.08:333][  0]LogUdpMessaging: Display: Added local interface '172.30.224.1' to multicast group '230.0.0.1:6666'
[2025.06.01-07.44.08:908][  0]LogNNERuntimeORT: Available graphics and compute adapters:
[2025.06.01-07.44.08:908][  0]LogNNERuntimeORT: 0: NVIDIA GeForce RTX 2080 Ti (Compute, Graphics)
[2025.06.01-07.44.08:908][  0]LogNNERuntimeORT: 1: AMD Radeon RX 6900 XT (Compute, Graphics)
[2025.06.01-07.44.08:908][  0]LogNNERuntimeORT: 2: Microsoft Basic Render Driver (Compute, Graphics)
[2025.06.01-07.44.08:908][  0]LogNNERuntimeORT: No NPU adapter found!
[2025.06.01-07.44.09:957][  0]LogMutable: Creating Mutable Customizable Object System.
[2025.06.01-07.44.11:671][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.8.dll
[2025.06.01-07.44.15:173][  0]LogSkeletalMesh: Building Skeletal Mesh Face_Archetype...
[2025.06.01-07.44.15:600][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (Face_Archetype) ...
[2025.06.01-07.44.22:336][  0]LogSkeletalMesh: Skeletal mesh [/MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.44.22:349][  0]LogSkeletalMesh: Built Skeletal Mesh [7.19s] /MetaHuman/IdentityTemplate/Face_Archetype.Face_Archetype
[2025.06.01-07.44.22:662][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.01-07.44.22:662][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.01-07.44.22:663][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.01-07.44.22:663][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.01-07.44.22:663][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.01-07.44.22:663][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.01-07.44.23:073][  0]LogAndroidPermission: UAndroidPermissionCallbackProxy::GetInstance
[2025.06.01-07.44.23:134][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.01-07.44.23:134][  0]LogAudioCaptureCore: Display: No Audio Capture implementations found. Audio input will be silent.
[2025.06.01-07.44.24:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/glf/resources/plugInfo.json'
[2025.06.01-07.44.24:394][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hd/resources/plugInfo.json'
[2025.06.01-07.44.24:406][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdSt/resources/plugInfo.json'
[2025.06.01-07.44.24:406][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdStorm/resources/plugInfo.json'
[2025.06.01-07.44.24:414][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdAbc/resources/plugInfo.json'
[2025.06.01-07.44.24:414][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hgiGL/resources/plugInfo.json'
[2025.06.01-07.44.24:414][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdrGlslfx/resources/plugInfo.json'
[2025.06.01-07.44.24:418][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShade/resources/plugInfo.json'
[2025.06.01-07.44.24:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProc/resources/plugInfo.json'
[2025.06.01-07.44.24:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ndr/resources/plugInfo.json'
[2025.06.01-07.44.24:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdProcImaging/resources/plugInfo.json'
[2025.06.01-07.44.24:426][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdx/resources/plugInfo.json'
[2025.06.01-07.44.24:447][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdGeom/resources/plugInfo.json'
[2025.06.01-07.44.24:468][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/plugInfo.json'
[2025.06.01-07.44.24:475][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/sdf/resources/plugInfo.json'
[2025.06.01-07.44.24:475][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdShaders/resources/plugInfo.json'
[2025.06.01-07.44.24:476][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImagingGL/resources/plugInfo.json'
[2025.06.01-07.44.24:487][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdPhysics/resources/plugInfo.json'
[2025.06.01-07.44.24:499][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdHydra/resources/plugInfo.json'
[2025.06.01-07.44.24:507][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMtlx/resources/plugInfo.json'
[2025.06.01-07.44.24:517][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRiPxrImaging/resources/plugInfo.json'
[2025.06.01-07.44.24:528][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkelImaging/resources/plugInfo.json'
[2025.06.01-07.44.24:538][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVol/resources/plugInfo.json'
[2025.06.01-07.44.24:550][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdMedia/resources/plugInfo.json'
[2025.06.01-07.44.24:577][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdImaging/resources/plugInfo.json'
[2025.06.01-07.44.24:601][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRender/resources/plugInfo.json'
[2025.06.01-07.44.24:612][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdVolImaging/resources/plugInfo.json'
[2025.06.01-07.44.24:627][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdUI/resources/plugInfo.json'
[2025.06.01-07.44.24:639][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdLux/resources/plugInfo.json'
[2025.06.01-07.44.24:647][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdRi/resources/plugInfo.json'
[2025.06.01-07.44.24:654][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usdSkel/resources/plugInfo.json'
[2025.06.01-07.44.24:659][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hio/resources/plugInfo.json'
[2025.06.01-07.44.24:659][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/ar/resources/plugInfo.json'
[2025.06.01-07.44.24:668][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/hdGp/resources/plugInfo.json'
[2025.06.01-07.44.24:669][  0]LogUsd: Updated LibraryPaths for USD plugInfo.json file 'D:/UE_5.5/Engine/Binaries/ThirdParty/USD/UsdResources/Win64/plugins/usd/resources/codegenTemplates/plugInfo.json'
[2025.06.01-07.44.26:120][  0]SourceControl: Revision control is disabled
[2025.06.01-07.44.26:329][  0]SourceControl: Revision control is disabled
[2025.06.01-07.44.26:577][  0]LogConfig: Branch 'ObjectMixerSerializedData' had been unloaded. Reloading on-demand took 0.56ms
[2025.06.01-07.44.26:664][  0]LogConfig: Branch 'Crypto' had been unloaded. Reloading on-demand took 0.47ms
[2025.06.01-07.44.30:671][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.01-07.44.30:671][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.01-07.44.31:239][  0]LogConfig: Applying CVar settings from Section [/Script/NNEDenoiser.NNEDenoiserSettings] File [Engine]
[2025.06.01-07.44.31:571][  0]LogCollectionManager: Loaded 0 collections in 0.001122 seconds
[2025.06.01-07.44.31:573][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Saved/Collections/' took 0.00s
[2025.06.01-07.44.31:576][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Developers/Shashank/Collections/' took 0.00s
[2025.06.01-07.44.31:576][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/Collections/' took 0.00s
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Initializing BlenderLink socket listener
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Set socket buffer sizes to 524288 bytes
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Binding socket to 127.0.0.1:2907
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Warning: Socket bind success: true
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Warning: Socket listen success: true
[2025.06.01-07.44.31:852][  0]LogBlenderLink: Waiting for client connection...
[2025.06.01-07.44.31:963][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png' error.
[2025.06.01-07.44.31:963][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Developer/PlasticSourceControl/Resources/Icon128.png
[2025.06.01-07.44.31:963][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png' error.
[2025.06.01-07.44.31:963][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_24x.png
[2025.06.01-07.44.31:963][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png' error.
[2025.06.01-07.44.31:963][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Platforms/VisionOS/Content/Editor/Slate/Launcher/Platform_VisionOS_128x.png
[2025.06.01-07.44.31:976][  0]LogStreaming: Warning: Failed to read file '../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png' error.
[2025.06.01-07.44.31:976][  0]LogSlate: Could not find file for Slate resource: ../../../Engine/Plugins/Interchange/Editor/Content/Old/Tiles/Outer/alertSolid.png
[2025.06.01-07.44.32:085][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Version 1.17.0-39599718 booting at 2025-06-01T07:44:32.085Z using C
[2025.06.01-07.44.32:095][  0]LogEOSSDK: LogEOS: [Boot] EOSSDK Platform Properties [OS=Windows/10.0.22621.4391.64bit, ClientId=xyza7891REBVsEqSJRRNXmlS7EQHM459, ProductId=86f32f1151354e7cb39c12f8ab2c22a3, SandboxId=********************************, DeploymentId=a652a72ea1664dcab3a467891eea5f30, ProductName=BlenderLinkProject, ProductVersion=++UE5+Release-5.5-***********, IsServer=false, Flags=DisableOverlay]
[2025.06.01-07.44.32:139][  0]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.01-07.44.32:143][  0]LogEOSSDK: LogEOSOverlay: Overlay will not load, because it was explicitly disabled when creating the platform
[2025.06.01-07.44.32:220][  0]LogEOSSDK: LogEOSAntiCheat: [AntiCheatClient] Anti-cheat client not available. Verify that the game was started using the anti-cheat bootstrapper if you intend to use it.
[2025.06.01-07.44.32:263][  0]LogEOSSDK: LogEOS: SetApplicationStatus - OldStatus: EOS_AS_Foreground, NewStatus: EOS_AS_Foreground, Current Time: 0001.01.01-00.00.00
[2025.06.01-07.44.32:263][  0]LogEOSSDK: LogEOS: SetNetworkStatus - OldStatus: EOS_NS_Online, NewStatus: EOS_NS_Online
[2025.06.01-07.44.32:272][  0]LogEOSSDK: LogEOS: Updating Platform SDK Config, Time: 0.008803
[2025.06.01-07.44.32:282][  0]LogFab: Display: Logging in using persist
[2025.06.01-07.44.32:283][  0]LogEOSSDK: Warning: LogEOSAuth: No existing persistent auth credentials were found for automatic login.
[2025.06.01-07.44.32:604][  0]LogUObjectArray: 52283 objects as part of root set at end of initial load.
[2025.06.01-07.44.32:604][  0]LogUObjectArray: CloseDisregardForGC: 0/0 objects in disregard for GC pool
[2025.06.01-07.44.32:617][  0]LogStreaming: Display: AsyncLoading2 - NotifyRegistrationComplete: Registered 38255 public script object entries (1030.17 KB)
[2025.06.01-07.44.32:617][  0]LogStreaming: Display: AsyncLoading2 - Thread Started: false, IsInitialLoad: false
[2025.06.01-07.44.33:283][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/OnlineSubsystemUtils.OnlineProxyStoreOffer. Time(ms): 10.3
[2025.06.01-07.44.33:291][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/JsonUtilities.JsonObjectWrapper. Time(ms): 8.0
[2025.06.01-07.44.33:301][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AudioExtensions.AudioParameter. Time(ms): 9.5
[2025.06.01-07.44.33:311][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/IrisCore.InstancedStructNetSerializerConfig. Time(ms): 9.7
[2025.06.01-07.44.33:321][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ToolWidgets.SidebarDrawerState. Time(ms): 9.9
[2025.06.01-07.44.33:326][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ToolWidgets.ActionButtonStyle. Time(ms): 4.1
[2025.06.01-07.44.33:345][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MaterialEditor.SortedParamData. Time(ms): 19.4
[2025.06.01-07.44.33:364][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GameplayTags.GameplayTagTableRow. Time(ms): 19.2
[2025.06.01-07.44.33:384][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AIModule.EnvQueryInstanceCache. Time(ms): 20.0
[2025.06.01-07.44.33:398][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AdvancedWidgets.ColorGradingSpinBoxStyle. Time(ms): 13.0
[2025.06.01-07.44.33:404][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AddContentDialog.FeatureAdditionalFiles. Time(ms): 6.1
[2025.06.01-07.44.33:410][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GameProjectGeneration.TemplateReplacement. Time(ms): 7.2
[2025.06.01-07.44.33:431][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/SceneOutliner.SceneOutlinerConfig. Time(ms): 19.3
[2025.06.01-07.44.33:450][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/PropertyEditor.DetailsViewConfig. Time(ms): 19.1
[2025.06.01-07.44.33:459][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Kismet.BPGraphClipboardData. Time(ms): 8.3
[2025.06.01-07.44.33:467][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/EditorConfig.EditorConfigTestSimpleMap. Time(ms): 8.8
[2025.06.01-07.44.33:479][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/EditorWidgets.FilterBarSettings. Time(ms): 11.5
[2025.06.01-07.44.33:486][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AnimGraphRuntime.AnimNode_SkeletalControlBase. Time(ms): 7.4
[2025.06.01-07.44.33:492][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AnimGraphRuntime.AnimNode_BlendSpacePlayerBase. Time(ms): 5.9
[2025.06.01-07.44.33:497][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AnimGraphRuntime.AnimNode_RigidBody. Time(ms): 4.6
[2025.06.01-07.44.33:509][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MovieSceneTracks.MovieSceneCVarOverrides. Time(ms): 11.8
[2025.06.01-07.44.33:517][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MovieScene.MovieSceneSubSequenceData. Time(ms): 7.3
[2025.06.01-07.44.33:528][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MediaUtils.MediaPlayerOptions. Time(ms): 11.4
[2025.06.01-07.44.33:551][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.ISMComponentDescriptorBase. Time(ms): 22.0
[2025.06.01-07.44.33:560][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.GPUSpriteEmitterInfo. Time(ms): 9.4
[2025.06.01-07.44.33:563][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.CompositeSection. Time(ms): 2.1
[2025.06.01-07.44.33:569][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.PropertyAccessLibrary. Time(ms): 6.4
[2025.06.01-07.44.33:572][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.RootMotionSource. Time(ms): 2.6
[2025.06.01-07.44.33:580][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.PlayerMuteList. Time(ms): 8.3
[2025.06.01-07.44.33:588][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.AutoCompleteNode. Time(ms): 7.7
[2025.06.01-07.44.33:595][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.LevelCollection. Time(ms): 6.6
[2025.06.01-07.44.33:601][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.WorldPartitionDestructibleHLODState. Time(ms): 5.9
[2025.06.01-07.44.33:606][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Engine.WorldPartitionUpdateStreamingCurrentState. Time(ms): 4.5
[2025.06.01-07.44.33:626][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AudioEditor.SoundCueGraphSchemaAction_NewNode. Time(ms): 20.0
[2025.06.01-07.44.33:637][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ChaosCaching.PerParticleCacheData. Time(ms): 11.3
[2025.06.01-07.44.33:647][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraSystemUpdateContext. Time(ms): 10.3
[2025.06.01-07.44.33:657][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraParameterStore. Time(ms): 8.9
[2025.06.01-07.44.33:665][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraComponentPropertyBinding. Time(ms): 8.9
[2025.06.01-07.44.33:673][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraOutlinerWorldData. Time(ms): 7.4
[2025.06.01-07.44.33:679][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Niagara.NiagaraUserRedirectionParameterStore. Time(ms): 6.5
[2025.06.01-07.44.33:687][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/TargetDeviceServices.TargetDeviceServicePong. Time(ms): 6.5
[2025.06.01-07.44.33:700][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Persona.PhysicsAssetRenderSettings. Time(ms): 13.0
[2025.06.01-07.44.33:714][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AutomationController.AutomationArtifact. Time(ms): 13.8
[2025.06.01-07.44.33:727][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObject.CustomizableObjectInstanceBakeOutput. Time(ms): 13.1
[2025.06.01-07.44.33:743][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/CustomizableObjectEditor.CustomizableObjectSchemaAction_NewNode. Time(ms): 14.9
[2025.06.01-07.44.33:756][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/RigVMDeveloper.RigVMInjectNodeIntoPinAction. Time(ms): 12.4
[2025.06.01-07.44.33:767][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRig.ControlRigSettingsPerPinBool. Time(ms): 10.3
[2025.06.01-07.44.33:782][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Serialization.StructSerializerObjectTestStruct. Time(ms): 15.0
[2025.06.01-07.44.33:784][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Serialization.StructSerializerLWCTypesTest. Time(ms): 1.1
[2025.06.01-07.44.33:796][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundFrontend.MetaSoundFrontendDocumentBuilder. Time(ms): 12.0
[2025.06.01-07.44.33:807][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/AudioWidgets.AudioMeterDefaultColorStyle. Time(ms): 9.9
[2025.06.01-07.44.33:820][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundEditor.MetasoundEditorMemberPageDefaultStringArray. Time(ms): 13.8
[2025.06.01-07.44.33:829][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetasoundEditor.MetasoundEditorGraphMemberNodeBreadcrumb. Time(ms): 9.4
[2025.06.01-07.44.33:843][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/StateTreeModule.GameplayTagQueryCondition. Time(ms): 13.3
[2025.06.01-07.44.33:851][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/StateTreeEditorModule.StateTreeCompilerLog. Time(ms): 7.9
[2025.06.01-07.44.33:858][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/StateTreeTestSuite.StateTreeTest_PropertyStructA. Time(ms): 7.0
[2025.06.01-07.44.33:870][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Synthesis.Synth2DSliderStyle. Time(ms): 11.0
[2025.06.01-07.44.33:880][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/TemplateSequence.TemplateSectionPropertyScale. Time(ms): 9.5
[2025.06.01-07.44.33:889][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/TranslationEditor.TranslationContextInfo. Time(ms): 9.2
[2025.06.01-07.44.33:894][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/PythonScriptPlugin.PyTestStruct. Time(ms): 4.6
[2025.06.01-07.44.33:903][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraSchemaAction_NewNode. Time(ms): 9.5
[2025.06.01-07.44.33:909][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraClipboardCurveCollection. Time(ms): 6.6
[2025.06.01-07.44.33:916][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraGraphScriptUsageInfo. Time(ms): 6.6
[2025.06.01-07.44.33:925][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraPropagatedVariable. Time(ms): 9.4
[2025.06.01-07.44.33:935][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/NiagaraEditor.NiagaraScriptVariableData. Time(ms): 9.8
[2025.06.01-07.44.33:943][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/InterchangeCommonParser.InterchangeStepCurve. Time(ms): 7.4
[2025.06.01-07.44.33:956][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/VariantManager.CapturableProperty. Time(ms): 12.8
[2025.06.01-07.44.33:967][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ConcertTransport.ConcertMessageData. Time(ms): 12.1
[2025.06.01-07.44.33:982][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/Concert.ConcertAdmin_GetSessionActivitiesResponse. Time(ms): 13.4
[2025.06.01-07.44.33:992][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ConcertClient.ConcertClientSettings. Time(ms): 10.7
[2025.06.01-07.44.34:000][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ConcertSyncCore.ConcertReplication_PutState_Request. Time(ms): 7.9
[2025.06.01-07.44.34:018][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MetaHumanCaptureSource.MetaHumanTakeInfo. Time(ms): 17.6
[2025.06.01-07.44.34:038][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/BuildPatchServices.FileManifestData. Time(ms): 20.1
[2025.06.01-07.44.34:048][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkMessageBusFramework.LiveLinkSubjectFrameMessage. Time(ms): 9.9
[2025.06.01-07.44.34:058][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkMovieScene.MovieSceneLiveLinkSectionTemplate. Time(ms): 9.3
[2025.06.01-07.44.34:078][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LiveLinkEditor.LiveLinkTestFrameDataInternal. Time(ms): 20.6
[2025.06.01-07.44.34:093][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LevelSequenceEditor.MovieSceneBindingPropertyInfo. Time(ms): 15.5
[2025.06.01-07.44.34:095][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/LevelSequenceEditor.LevelSequenceTrackSettings. Time(ms): 1.3
[2025.06.01-07.44.34:108][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ObjectMixerEditor.ObjectMixerCollectionObjectSet. Time(ms): 12.4
[2025.06.01-07.44.34:112][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/MeshModelingToolsExp.PhysicsBoxData. Time(ms): 4.5
[2025.06.01-07.44.34:118][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.AnimLayerSelectionSet. Time(ms): 6.1
[2025.06.01-07.44.34:125][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.AnimLayerItem. Time(ms): 6.2
[2025.06.01-07.44.34:128][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.AnimLayerState. Time(ms): 3.3
[2025.06.01-07.44.34:137][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/ControlRigEditor.ControlRigSnapperSelection. Time(ms): 9.1
[2025.06.01-07.44.34:145][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GeometryCollectionNodes.UniformVectorFieldDataflowNode. Time(ms): 7.4
[2025.06.01-07.44.34:154][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/GeometryCollectionNodes.AutoClusterDataflowNode. Time(ms): 9.2
[2025.06.01-07.44.34:162][  0]LogClass: Display: AttemptToFindUninitializedScriptStructMembers took more than 1ms to process ScriptStruct /Script/InterchangeTests.InterchangeTestFunctionResult. Time(ms): 5.8
[2025.06.01-07.44.34:494][  0]LogEngine: Initializing Engine...
[2025.06.01-07.44.34:819][  0]LogStylusInput: Initializing StylusInput subsystem.
[2025.06.01-07.44.34:856][  0]LogStats: UGameplayTagsManager::InitializeManager -  0.009 s
[2025.06.01-07.44.35:583][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.11.dll
[2025.06.01-07.44.36:038][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world Untitled
[2025.06.01-07.44.36:954][  0]LogNetVersion: Set ProjectVersion to *******. Version Checksum will be recalculated on next use.
[2025.06.01-07.44.36:954][  0]LogInit: Texture streaming: Enabled
[2025.06.01-07.44.37:045][  0]LogAnalytics: Display: [UEEditor.Rocket.Release] APIServer = https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data. AppVersion = 5.5.4-40574608+++UE5+Release-5.5
[2025.06.01-07.44.37:124][  0]LogAudio: Display: Initializing Audio Device Manager...
[2025.06.01-07.44.37:199][  0]LogAudio: Display: Loading Default Audio Settings Objects...
[2025.06.01-07.44.37:204][  0]LogAudio: Display: No default SoundConcurrencyObject specified (or failed to load).
[2025.06.01-07.44.37:205][  0]LogAudio: Display: Audio Device Manager Initialized
[2025.06.01-07.44.37:225][  0]LogAudio: Display: Creating Audio Device:                 Id: 1, Scope: Shared, Realtime: True
[2025.06.01-07.44.37:225][  0]LogAudioMixer: Display: Audio Mixer Platform Settings:
[2025.06.01-07.44.37:225][  0]LogAudioMixer: Display: 	Sample Rate:						  48000
[2025.06.01-07.44.37:225][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size Requested: 1024
[2025.06.01-07.44.37:246][  0]LogAudioMixer: Display: 	Callback Buffer Frame Size To Use:	  1024
[2025.06.01-07.44.37:246][  0]LogAudioMixer: Display: 	Number of buffers to queue:			  1
[2025.06.01-07.44.37:246][  0]LogAudioMixer: Display: 	Max Channels (voices):				  32
[2025.06.01-07.44.37:246][  0]LogAudioMixer: Display: 	Number of Async Source Workers:		  4
[2025.06.01-07.44.37:246][  0]LogAudio: Display: AudioDevice MaxSources: 32
[2025.06.01-07.44.37:261][  0]LogAudio: Display: Audio Spatialization Plugin: None (built-in).
[2025.06.01-07.44.37:261][  0]LogAudio: Display: Audio Reverb Plugin: None (built-in).
[2025.06.01-07.44.37:261][  0]LogAudio: Display: Audio Occlusion Plugin: None (built-in).
[2025.06.01-07.44.37:301][  0]LogAudioMixer: Display: Initializing audio mixer using platform API: 'XAudio2'
[2025.06.01-07.44.37:359][  0]LogAudioMixer: Display: Using Audio Hardware Device Voicemeeter Input (VB-Audio Voicemeeter VAIO)
[2025.06.01-07.44.37:372][  0]LogAudioMixer: Display: Initializing Sound Submixes...
[2025.06.01-07.44.37:447][  0]LogAudioMixer: Display: Creating Master Submix 'MasterSubmixDefault'
[2025.06.01-07.44.37:447][  0]LogAudioMixer: Display: Creating Master Submix 'MasterReverbSubmixDefault'
[2025.06.01-07.44.37:473][  0]LogAudioMixer: FMixerPlatformXAudio2::StartAudioStream() called. InstanceID=1
[2025.06.01-07.44.37:473][  0]LogAudioMixer: Display: Output buffers initialized: Frames=1024, Channels=2, Samples=2048, InstanceID=1
[2025.06.01-07.44.37:476][  0]LogAudioMixer: Display: Starting AudioMixerPlatformInterface::RunInternal(), InstanceID=1
[2025.06.01-07.44.37:476][  0]LogAudioMixer: Display: FMixerPlatformXAudio2::SubmitBuffer() called for the first time. InstanceID=1
[2025.06.01-07.44.37:500][  0]LogInit: FAudioDevice initialized with ID 1.
[2025.06.01-07.44.37:500][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'Untitled'.
[2025.06.01-07.44.37:505][  0]LogAudioMixer: Initializing Audio Bus Subsystem for audio device with ID 1
[2025.06.01-07.44.37:638][  0]LogCsvProfiler: Display: Metadata set : largeworldcoordinates="1"
[2025.06.01-07.44.37:707][  0]LogInit: Undo buffer set to 256 MB
[2025.06.01-07.44.37:716][  0]LogInit: Transaction tracking system initialized
[2025.06.01-07.44.37:868][  0]LogSourceControl: Display: Uncontrolled Changelist persistency file loaded H:/Plugins/BlenderLinkProject/Saved/SourceControl/UncontrolledChangelists.json
[2025.06.01-07.44.38:872][  0]LogConfig: Branch 'LocalizationServiceSettings' had been unloaded. Reloading on-demand took 0.53ms
[2025.06.01-07.44.38:873][  0]LocalizationService: Localization service is disabled
[2025.06.01-07.44.39:078][  0]LogTimingProfiler: Initialize
[2025.06.01-07.44.39:095][  0]LogTimingProfiler: OnSessionChanged
[2025.06.01-07.44.39:095][  0]LoadingProfiler: Initialize
[2025.06.01-07.44.39:095][  0]LoadingProfiler: OnSessionChanged
[2025.06.01-07.44.39:104][  0]LogNetworkingProfiler: Initialize
[2025.06.01-07.44.39:107][  0]LogNetworkingProfiler: OnSessionChanged
[2025.06.01-07.44.39:107][  0]LogMemoryProfiler: Initialize
[2025.06.01-07.44.39:108][  0]LogMemoryProfiler: OnSessionChanged
[2025.06.01-07.44.42:149][  0]LogAutoReimportManager: Warning: Unable to watch directory H:/Plugins/BlenderLinkProject/Content/ as it will conflict with another watching H:/Plugins/BlenderLinkProject/Content/.
[2025.06.01-07.44.42:157][  0]LogFileCache: Scanning file cache for directory 'H:/Plugins/BlenderLinkProject/Content/' took 0.01s
[2025.06.01-07.44.45:127][  0]LogPython: Using Python 3.11.8
[2025.06.01-07.44.48:232][  0]LogPython: Display: No enabled plugins with python dependencies found, skipping
[2025.06.01-07.44.48:626][  0]LogRenderer: Requested compilation of Path Tracing RTPSOs (1 permutations).
[2025.06.01-07.44.50:088][  0]LogStreaming: Warning: Failed to read file 'Common/Selector.png' error.
[2025.06.01-07.44.50:088][  0]LogSlate: Could not find file for Slate resource: Common/Selector.png
[2025.06.01-07.44.50:766][  0]LogLevelSequenceEditor: LevelSequenceEditor subsystem initialized.
[2025.06.01-07.44.51:407][  0]LogEditorDataStorage: Initializing
[2025.06.01-07.44.51:445][  0]LogEditorDataStorage: Initialized
[2025.06.01-07.44.51:447][  0]LogWindows: Attached monitors:
[2025.06.01-07.44.51:447][  0]LogWindows:     resolution: 3840x2160, work area: (0, 0) -> (3840, 2112), device: '\\.\DISPLAY8' [PRIMARY]
[2025.06.01-07.44.51:447][  0]LogWindows:     resolution: 1920x1080, work area: (3840, 1071) -> (5760, 2103), device: '\\.\DISPLAY1'
[2025.06.01-07.44.51:447][  0]LogWindows:     resolution: 1920x1080, work area: (3840, -9) -> (5760, 1023), device: '\\.\DISPLAY2'
[2025.06.01-07.44.51:447][  0]LogWindows: Found 3 attached monitors.
[2025.06.01-07.44.51:447][  0]LogWindows: Gathering driver information using Windows Setup API
[2025.06.01-07.44.51:480][  0]LogAssetRegistry: Display: Triggering cache save on discovery complete
[2025.06.01-07.44.51:628][  0]LogInit: Display: Engine is initialized. Leaving FEngineLoop::Init()
[2025.06.01-07.44.51:764][  0]SourceControl: Revision control is disabled
[2025.06.01-07.44.51:773][  0]LogUnrealEdMisc: Loading editor; pre map load, took 148.024
[2025.06.01-07.44.51:903][  0]Cmd: MAP LOAD FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/TestLevel.umap" TEMPLATE=0 SHOWPROGRESS=1 FEATURELEVEL=4
[2025.06.01-07.44.51:987][  0]LogWorld: UWorld::CleanupWorld for Untitled, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.44.52:039][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.44.52:256][  0]LogAssetRegistry: Display: Asset registry cache written as 44.1 MiB to H:/Plugins/BlenderLinkProject/Intermediate/CachedAssetRegistry_*.bin
[2025.06.01-07.44.52:524][  0]LogAudio: Display: Audio Device unregistered from world 'None'.
[2025.06.01-07.44.52:840][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.53ms
[2025.06.01-07.44.52:943][  0]LogAudio: Display: Audio Device (ID: 1) registered with world 'TestLevel'.
[2025.06.01-07.44.52:943][  0]LogChaosDD: Creating Chaos Debug Draw Scene for world TestLevel
[2025.06.01-07.44.53:028][  0]LogWorldPartition: ULevel::OnLevelLoaded(TestLevel)(bIsOwningWorldGameWorld=0, bIsOwningWorldPartitioned=1, InitializeForMainWorld=1, InitializeForEditor=1, InitializeForGame=0)
[2025.06.01-07.44.53:029][  0]LogWorldPartition: Display: WorldPartition initialize started...
[2025.06.01-07.44.53:029][  0]LogWorldPartition: UWorldPartition::Initialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel, World Type = Editor, IsMainWorldPartition = 1, Location = V(0), Rotation = R(0), IsEditor = 1, IsGame = 0, IsPIEWorldTravel = 0, IsCooking = 0
[2025.06.01-07.44.56:597][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.01-07.44.56:609][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.01-07.44.56:611][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.01-07.44.56:612][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [AppleTV]
[2025.06.01-07.44.56:612][  0]LogDeviceProfileManager: Going up to parent DeviceProfile [IOS]
[2025.06.01-07.44.56:612][  0]LogDeviceProfileManager: Going up to parent DeviceProfile []
[2025.06.01-07.44.56:613][  0]LogDeviceProfileManager: Display: Deviceprofile None not found.
[2025.06.01-07.45.01:700][  0]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.7.dll
[2025.06.01-07.45.01:806][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_btm_jeans_nrm_Cinematic...
[2025.06.01-07.45.02:191][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.45.02:195][  0]LogSkeletalMesh: Built Skeletal Mesh [0.39s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Bottoms/Jeans/m_med_nrw_btm_jeans_nrm_Cinematic.m_med_nrw_btm_jeans_nrm_Cinematic
[2025.06.01-07.45.02:208][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_top_crewneckt_nrm_Cinematic...
[2025.06.01-07.45.02:209][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_shs_runningshoes_Cinematic...
[2025.06.01-07.45.02:591][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.45.02:593][  0]LogSkeletalMesh: Built Skeletal Mesh [0.38s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Shoes/RunningShoes/m_med_nrw_shs_runningshoes_Cinematic.m_med_nrw_shs_runningshoes_Cinematic
[2025.06.01-07.45.03:683][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.45.03:686][  0]LogSkeletalMesh: Built Skeletal Mesh [1.48s] /Game/MetaHumans/Common/Male/Medium/NormalWeight/Tops/Crewneckt/m_med_nrw_top_crewneckt_nrm_Cinematic.m_med_nrw_top_crewneckt_nrm_Cinematic
[2025.06.01-07.45.03:801][  0]LogSkeletalMesh: Building Skeletal Mesh m_med_nrw_body...
[2025.06.01-07.45.04:015][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.45.04:018][  0]LogSkeletalMesh: Built Skeletal Mesh [0.22s] /Game/MetaHumans/MH_Friend/Body/m_med_nrw_body.m_med_nrw_body
[2025.06.01-07.45.04:036][  0]LogSkeletalMesh: Building Skeletal Mesh MH_Friend_FaceMesh...
[2025.06.01-07.45.04:529][  0]LogWorldPartition: Display: WorldPartition initialize took 11.5 sec
[2025.06.01-07.45.06:193][  0]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend_FaceMesh) ...
[2025.06.01-07.45.06:935][  0]LogDerivedDataCache: C:/Users/<USER>/AppData/Local/UnrealEngine/Common/DerivedDataCache: Maintenance finished in +00:00:00.000 and deleted 0 files with total size 0 MiB and 0 empty folders. Scanned 0 files in 1 folders with total size 0 MiB.
[2025.06.01-07.45.09:712][  0]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.45.09:727][  0]LogSkeletalMesh: Built Skeletal Mesh [5.69s] /Game/MetaHumans/MH_Friend/Face/MH_Friend_FaceMesh.MH_Friend_FaceMesh
[2025.06.01-07.45.10:896][  0]LogEditorServer: Finished looking for orphan Actors (0.000 secs)
[2025.06.01-07.45.11:507][  0]LogUObjectHash: Compacting FUObjectHashTables data took   0.87ms
[2025.06.01-07.45.11:546][  0]Cmd: MAP CHECKDEP NOCLEARLOG
[2025.06.01-07.45.11:564][  0]MapCheck: Map check complete: 0 Error(s), 0 Warning(s), took 18.116ms to complete.
[2025.06.01-07.45.12:447][  0]LogUnrealEdMisc: Total Editor Startup Time, took 168.697
[2025.06.01-07.45.13:327][  0]LogActorFactory: Loading ActorFactory Class /Script/Engine.LevelInstance
[2025.06.01-07.45.14:271][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.01-07.45.14:330][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.01-07.45.14:366][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.01-07.45.14:403][  0]LogSlate: Updating window title bar state: overlay mode, drag disabled, window buttons hidden, title bar hidden
[2025.06.01-07.45.15:066][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:106][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/StarterContent.upack', mount point: 'root:/'
[2025.06.01-07.45.15:120][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:133][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPerson.upack', mount point: 'root:/'
[2025.06.01-07.45.15:134][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:138][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_FirstPersonBP.upack', mount point: 'root:/'
[2025.06.01-07.45.15:139][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:151][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_HandheldARBP.upack', mount point: 'root:/'
[2025.06.01-07.45.15:168][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:174][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPerson.upack', mount point: 'root:/'
[2025.06.01-07.45.15:183][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:191][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_ThirdPersonBP.upack', mount point: 'root:/'
[2025.06.01-07.45.15:192][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:197][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDown.upack', mount point: 'root:/'
[2025.06.01-07.45.15:198][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:209][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_TopDownBP.upack', mount point: 'root:/'
[2025.06.01-07.45.15:210][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:214][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VehicleAdvBP.upack', mount point: 'root:/'
[2025.06.01-07.45.15:215][  0]LogPakFile: Initializing PakPlatformFile
[2025.06.01-07.45.15:224][  0]LogPakFile: Display: Mounted Pak file 'D:/UE_5.5/FeaturePacks/TP_VirtualRealityBP.upack', mount point: 'root:/'
[2025.06.01-07.45.17:543][  0]LogSlate: Took 0.006973 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/DroidSansMono.ttf' (77K)
[2025.06.01-07.45.18:207][  0]LogStall: Startup...
[2025.06.01-07.45.18:211][  0]LogStall: Startup complete.
[2025.06.01-07.45.18:279][  0]LogLoad: (Engine Initialization) Total time: 174.53 seconds
[2025.06.01-07.45.18:879][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/CX/RO749IAXISCV8LT9J5BYI5) ...
[2025.06.01-07.45.19:011][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/0/GK/X2E2F4C7MIW7ZEGZ962NPQ) ...
[2025.06.01-07.45.19:111][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/1/2F/MHEXK6BJ1KVAC7HBYSCFW0) ...
[2025.06.01-07.45.19:189][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/7/40/3X2YNQMQX451AT39W6TXU8) ...
[2025.06.01-07.45.19:233][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/2P/7D2L64CH3U27EU4ATOC2OJ) ...
[2025.06.01-07.45.19:309][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/8/KN/A8O4S8R7MMUVCDRNUYVZIP) ...
[2025.06.01-07.45.19:344][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/9/3R/1ET1SFUZINXRYGHWRR7L3U) ...
[2025.06.01-07.45.19:386][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/A/C5/IVDKMVVBTGTDXFK9JQC0BM) ...
[2025.06.01-07.45.19:468][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/A/FE/0GTAWQZWTNFD5WKTIFF37W) ...
[2025.06.01-07.45.19:499][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/A/KQ/XZI6D32LS8KYG1A6YGPPET) ...
[2025.06.01-07.45.19:531][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/B/SX/SKC4RM47NC17VN52UHPG2X) ...
[2025.06.01-07.45.19:566][  0]LogStaticMesh: Display: Waiting for static meshes to be ready 0/1 (/Game/__ExternalActors__/MetaHumans/Test/TestLevel/E/6L/1MWYH071IEHTL8SURM21XO) ...
[2025.06.01-07.45.19:747][  0]LogContentStreaming: Texture pool size now 1000 MB
[2025.06.01-07.45.19:747][  0]LogCsvProfiler: Display: Metadata set : streamingpoolsizemb="1000"
[2025.06.01-07.45.20:146][  0]LogSlate: Took 0.015175 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Regular.ttf' (155K)
[2025.06.01-07.45.20:248][  0]LogSlate: Took 0.014078 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Bold.ttf' (160K)
[2025.06.01-07.45.20:266][  0]LogSlate: Took 0.017025 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Italic.ttf' (157K)
[2025.06.01-07.45.20:416][  0]LogNNEDenoiser: ApplySettings: bDenoiserEnabled 1
[2025.06.01-07.45.20:416][  0]LogStreaming: Display: FlushAsyncLoading(501): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.45.20:487][  0]LogNNEDenoiser: Loaded input mapping from NNEDIM_ColorAlbedoNormal_Alpha
[2025.06.01-07.45.20:488][  0]LogNNEDenoiser: Loaded output mapping from NNEDOM_Output_Alpha
[2025.06.01-07.45.20:488][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.01-07.45.20:874][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.01-07.45.20:874][  0]LogNNEDenoiser: Create denoiser from asset /NNEDenoiser/NNED_Oidn2-3_Balanced_Alpha.NNED_Oidn2-3_Balanced_Alpha...
[2025.06.01-07.45.20:896][  0]LogNNEDenoiser: Loaded input mapping from NNEDTIM_ColorAlbedoNormal_Alpha
[2025.06.01-07.45.20:896][  0]LogNNEDenoiser: Loaded output mapping from NNEDTOM_Output_Alpha
[2025.06.01-07.45.20:896][  0]LogNNEDenoiser: Try create model instance with runtime NNERuntimeORTDml on RDG...
[2025.06.01-07.45.20:952][  0]LogNNEDenoiser: Display: Created model instance with runtime NNERuntimeORTDml on RDG
[2025.06.01-07.45.20:953][  0]LogNNEDenoiser: Create temporal denoiser from asset /NNEDenoiser/NNEDT_Oidn2-3_Balanced_Alpha.NNEDT_Oidn2-3_Balanced_Alpha...
[2025.06.01-07.45.21:736][  0]LogSlate: Took 0.012400 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.01-07.45.22:004][  0]LogSlate: Warning: Unable to rasterize '../../../Engine/Content/Editor/Slate/Starship/AssetIcons/SkeletalMesh_64.svg'. File could not be found
[2025.06.01-07.45.22:264][  0]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.45.22:391][  0]LogFab: Error: Login failed - error code: EOS_InvalidAuth
[2025.06.01-07.45.22:391][  0]LogFab: Display: Logging in using exchange code
[2025.06.01-07.45.22:391][  0]LogFab: Display: Reading exchange code from commandline
[2025.06.01-07.45.22:391][  0]LogEOSSDK: Error: LogEOSAuth: Invalid parameter EOS_Auth_Credentials.Token reason: must not be null or empty
[2025.06.01-07.45.22:487][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... started...
[2025.06.01-07.45.22:726][  0]LogD3D12RHI: Creating RTPSO with 21 shaders (0 cached, 21 new) took 10.87 ms. Compile time 7.10 ms, link time 3.66 ms.
[2025.06.01-07.45.23:903][  0]LogPython: registering <class 'ControlRigWorkflows.workflow_deformation_rig_preset.provider'>

[2025.06.01-07.45.23:917][  0]LogPython: Display: Running start-up script D:/UE_5.5/Engine/Plugins/Animation/ControlRig/Content/Python/init_unreal.py... took 1.43 sec
[2025.06.01-07.45.24:309][  0]LogLiveCoding: Display: LiveCodingConsole Arguments: BlenderLinkProjectEditor Win64 Development
[2025.06.01-07.45.24:474][  1]LogAssetRegistry: AssetRegistryGather time 4.6433s: AssetDataDiscovery 4.1424s, AssetDataGather 0.1886s, StoreResults 0.3123s. Wall time 119.9290s.
	NumCachedDirectories 0. NumUncachedDirectories 1886. NumCachedFiles 8088. NumUncachedFiles 2.
	BackgroundTickInterruptions 0.
[2025.06.01-07.45.24:578][  1]LogSourceControl: Uncontrolled asset enumeration started...
[2025.06.01-07.45.24:578][  1]LogCollectionManager: Fixed up redirectors for 0 collections in 0.000000 seconds (updated 0 objects)
[2025.06.01-07.45.25:123][  1]LogFab: Error: Login failed - error code: EOS_InvalidParameters
[2025.06.01-07.45.25:429][  2]LogEOSSDK: LogEOS: SDK Config Platform Update Request Successful, Time: 52.859539
[2025.06.01-07.45.25:430][  2]LogEOSSDK: LogEOSAnalytics: EOS SDK Analytics disabled for route [1].
[2025.06.01-07.45.25:468][  2]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 53.155270
[2025.06.01-07.45.26:244][ 10]LogEOSSDK: LogEOSAnalytics: Start Session (User: ...)
[2025.06.01-07.45.26:798][ 17]LogEOSSDK: LogEOS: SDK Config Product Update Request Successful, Time: 54.451393
[2025.06.01-07.45.26:800][ 17]LogEOSSDK: LogEOS: SDK Config Data - Watermark: 348165457
[2025.06.01-07.45.26:800][ 17]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 54.451393, Update Interval: 336.413452
[2025.06.01-07.45.28:384][ 42]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.45.30:737][ 82]LogSourceControl: Uncontrolled asset enumeration finished in 6.158737 seconds (Found 8066 uncontrolled assets)
[2025.06.01-07.45.38:437][214]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.45.48:460][389]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.45.58:510][558]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.46.08:555][731]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.46.18:606][901]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.46.28:633][ 71]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.46.38:657][243]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.46.48:700][417]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.46.58:702][591]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.47.08:710][760]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.47.18:758][929]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.47.28:819][100]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.47.38:856][270]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.47.48:906][419]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.47.50:514][436]LogStreaming: Display: FlushAsyncLoading(511): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.47.50:830][436]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend) ...
[2025.06.01-07.47.51:181][436]LogUObjectHash: Compacting FUObjectHashTables data took   1.08ms
[2025.06.01-07.47.51:561][436]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_0
[2025.06.01-07.47.52:636][436]LogSlate: Window 'Delete Assets' being destroyed
[2025.06.01-07.47.52:929][436]LogUObjectHash: Compacting FUObjectHashTables data took   0.70ms
[2025.06.01-07.47.52:947][436]LogUObjectHash: Compacting FUObjectHashTables data took   0.69ms
[2025.06.01-07.47.52:963][436]LogUObjectHash: Compacting FUObjectHashTables data took   0.37ms
[2025.06.01-07.47.58:904][537]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.48.05:633][656]LogStreaming: Display: FlushAsyncLoading(512): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.48.05:855][656]LogInterchangeEngine: Display: Interchange start importing source [C:/Users/<USER>/Desktop/Export/MH_Friend.fbx]
[2025.06.01-07.48.35:793][656]LogSlate: Took 0.000087 seconds to synchronously load lazily loaded font '../../../Engine/Content/Slate/Fonts/Roboto-Light.ttf' (167K)
[2025.06.01-07.48.35:858][656]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.10.dll
[2025.06.01-07.48.35:884][656]LogTextureFormatOodle: Display: Oodle Texture loading DLL: oo2tex_win64_2.9.6.dll
[2025.06.01-07.48.35:904][656]LogSkeletalMesh: Building Skeletal Mesh SKM_Face_Preview...
[2025.06.01-07.48.36:063][656]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_1
[2025.06.01-07.48.36:152][656]LogSkeletalMesh: Skeletal mesh [/Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview]: The derived data key is different after the build. Save the asset to avoid rebuilding it everytime the editor load it.
[2025.06.01-07.48.36:155][656]LogSkeletalMesh: Built Skeletal Mesh [0.25s] /Game/MetaHumans/Common/Face/SKM_Face_Preview.SKM_Face_Preview
[2025.06.01-07.48.47:066][656]LogSlate: Window 'Import Content' being destroyed
[2025.06.01-07.48.47:137][656]LogInterchangeEngine: [Pending] Importing
[2025.06.01-07.48.47:286][657]LogStreaming: Display: FlushAsyncLoading(518): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.48.47:287][657]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.48.52:769][721]LogStreaming: Display: FlushAsyncLoading(519): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.48.54:691][741]LogSkeletalMesh: Building Skeletal Mesh MH_Friend...
[2025.06.01-07.48.57:470][769]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.01:122][790]LogSkeletalMesh: Built Skeletal Mesh [6.43s] /Game/MetaHumans/Test/MH_Friend.MH_Friend
[2025.06.01-07.49.01:573][791]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend) ...
[2025.06.01-07.49.01:615][791]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_2
[2025.06.01-07.49.01:625][791]LogSkeletalMesh: USkeletalMeshComponent::InitArticulated : Could not find root physics body: '/Engine/Transient.World_2:PersistentLevel.SkeletalMeshActor_0.SkeletalMeshComponent0'
[2025.06.01-07.49.01:625][791]LogWorld: UWorld::CleanupWorld for World_2, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.49.01:625][791]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.49.01:631][791]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_3
[2025.06.01-07.49.01:672][791]LogWorld: UWorld::CleanupWorld for World_3, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.49.01:672][791]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.49.01:728][792]LogUObjectHash: Compacting FUObjectHashTables data took   0.96ms
[2025.06.01-07.49.01:955][792]LogInterchangeEngine: Display: Interchange import completed [C:/Users/<USER>/Desktop/Export/MH_Friend.fbx]
[2025.06.01-07.49.01:977][792]LogInterchangeEngine: [Pending] Importing - Operation completed.
[2025.06.01-07.49.01:977][792]LogInterchangeEngine: [Success] Import Done
[2025.06.01-07.49.01:980][793]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_4
[2025.06.01-07.49.07:432][867]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.17:452][ 47]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.27:537][227]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.29:227][249]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Test/MH_Friend.MH_Friend
[2025.06.01-07.49.29:529][249]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_5
[2025.06.01-07.49.29:539][249]LogStreaming: Display: FlushAsyncLoading(520): 1 QueuedPackages, 0 AsyncPackages
[2025.06.01-07.49.29:806][249]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_5:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.01-07.49.32:287][251]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_6
[2025.06.01-07.49.37:524][348]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.43:139][865]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_7
[2025.06.01-07.49.44:052][943]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_8
[2025.06.01-07.49.44:101][944]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_9
[2025.06.01-07.49.44:162][945]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_10
[2025.06.01-07.49.46:823][982]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend) ...
[2025.06.01-07.49.46:984][982]LogSlate: Took 0.008427 seconds to synchronously load lazily loaded font '../../../Engine/Content/Editor/Slate/Fonts/FontAwesome.ttf' (139K)
[2025.06.01-07.49.47:491][ 29]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.51:350][285]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_11
[2025.06.01-07.49.51:650][289]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_12
[2025.06.01-07.49.51:731][290]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_13
[2025.06.01-07.49.51:792][291]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_14
[2025.06.01-07.49.51:852][292]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_15
[2025.06.01-07.49.57:599][367]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.49.57:915][370]LogSkeletalMesh: Display: Waiting for skinned assets to be ready 0/1 (MH_Friend) ...
[2025.06.01-07.49.59:279][472]LogUObjectHash: Compacting FUObjectHashTables data took   0.83ms
[2025.06.01-07.50.00:565][472]LogSlate: Window 'Save Content' being destroyed
[2025.06.01-07.50.00:575][472]LogFileHelpers: InternalPromptForCheckoutAndSave started...
[2025.06.01-07.50.00:643][472]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Test/MH_Friend] ([1] browsable assets)...
[2025.06.01-07.50.00:650][472]OBJ SavePackage:     Rendered thumbnail for [SkeletalMesh /Game/MetaHumans/Test/MH_Friend.MH_Friend]
[2025.06.01-07.50.00:650][472]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test/MH_Friend]
[2025.06.01-07.50.00:650][472]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Test/MH_Friend" FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/MH_Friend.uasset" SILENT=true
[2025.06.01-07.50.01:032][472]LogSavePackage: Moving output files for package: /Game/MetaHumans/Test/MH_Friend
[2025.06.01-07.50.01:032][472]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend1CB585A2408ED26BB07620BEAF51A18E.tmp' to 'H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/MH_Friend.uasset'
[2025.06.01-07.50.01:078][472]OBJ SavePackage: Generating thumbnails for [1] asset(s) in package [/Game/MetaHumans/Test/MH_Friend_PhysicsAsset] ([1] browsable assets)...
[2025.06.01-07.50.01:095][472]OBJ SavePackage:     Rendered thumbnail for [PhysicsAsset /Game/MetaHumans/Test/MH_Friend_PhysicsAsset.MH_Friend_PhysicsAsset]
[2025.06.01-07.50.01:095][472]OBJ SavePackage: Finished generating thumbnails for package [/Game/MetaHumans/Test/MH_Friend_PhysicsAsset]
[2025.06.01-07.50.01:096][472]Cmd: OBJ SAVEPACKAGE PACKAGE="/Game/MetaHumans/Test/MH_Friend_PhysicsAsset" FILE="H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/MH_Friend_PhysicsAsset.uasset" SILENT=true
[2025.06.01-07.50.01:097][472]LogSavePackage: Moving output files for package: /Game/MetaHumans/Test/MH_Friend_PhysicsAsset
[2025.06.01-07.50.01:097][472]LogSavePackage: Moving 'H:/Plugins/BlenderLinkProject/Saved/MH_Friend_PhysicsAssetFE0615A540B5268A0A533D92EE6DFA57.tmp' to 'H:/Plugins/BlenderLinkProject/Content/MetaHumans/Test/MH_Friend_PhysicsAsset.uasset'
[2025.06.01-07.50.01:117][472]LogFileHelpers: InternalPromptForCheckoutAndSave took 542.512 ms
[2025.06.01-07.50.01:183][472]LogContentValidation: Display: Starting to validate 2 assets
[2025.06.01-07.50.01:183][472]LogContentValidation: Enabled validators:
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/DataValidation.EditorValidator_Material
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/DataValidation.DirtyFilesChangelistValidator
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/DataValidation.EditorValidator_Localization
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/DataValidation.PackageFileValidator
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/DataValidation.WorldPartitionChangelistValidator
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/InputBlueprintNodes.EnhancedInputUserWidgetValidator
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/MutableValidation.AssetValidator_CustomizableObjects
[2025.06.01-07.50.01:183][472]LogContentValidation: 	/Script/MutableValidation.AssetValidator_ReferencedCustomizableObjects
[2025.06.01-07.50.01:184][472]AssetCheck: /Game/MetaHumans/Test/MH_Friend Validating asset
[2025.06.01-07.50.01:184][472]AssetCheck: /Game/MetaHumans/Test/MH_Friend_PhysicsAsset Validating asset
[2025.06.01-07.50.03:162][625]LogWorld: UWorld::CleanupWorld for World_5, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.50.03:162][625]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.50.03:223][625]LogUObjectHash: Compacting FUObjectHashTables data took   0.78ms
[2025.06.01-07.50.09:877][727]LogFactory: FactoryCreateFile: DNAAsset with DNAAssetImportFactory (0 0 C:/Users/<USER>/Desktop/Export/edited_dna.dna)
[2025.06.01-07.50.11:340][727]LogSlate: Window 'DNA Import Options' being destroyed
[2025.06.01-07.50.11:545][727]LogRigLogicEditor: Error: Reimporting of DNA failed
[2025.06.01-07.50.11:686][728]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.50.14:258][773]LogAssetEditorSubsystem: Opening Asset editor for SkeletalMesh /Game/MetaHumans/Test/MH_Friend.MH_Friend
[2025.06.01-07.50.14:259][773]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_16
[2025.06.01-07.50.14:370][773]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_16:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.01-07.50.21:597][408]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.50.31:597][460]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.50.41:601][516]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.50.51:607][575]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.50.55:458][983]LogAssetEditorSubsystem: Opening Asset editor for AnimSequence /Game/MetaHumans/Common/Common/Mocap/mh_arkit_mapping_anim.mh_arkit_mapping_anim
[2025.06.01-07.50.55:467][983]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_17
[2025.06.01-07.50.55:487][983]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_17:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.01-07.50.55:729][983]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_18
[2025.06.01-07.50.55:862][983]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_17:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.01-07.50.56:044][983]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_19
[2025.06.01-07.50.56:176][983]LogUObjectGlobals: Warning: Failed to find object 'Class /Script/ApexDestruction.DestructibleMesh'
[2025.06.01-07.50.56:181][983]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_20
[2025.06.01-07.50.58:216][ 77]LogChaosDD: Not creating Chaos Debug Draw Scene for world World_21
[2025.06.01-07.50.59:921][ 99]LogActorComponent: RegisterComponentWithWorld: (/Engine/Transient.World_17:PersistentLevel.AnimationEditorPreviewActor_0.DebugSkelMeshComponent_0) Already registered. Aborting.
[2025.06.01-07.51.01:631][180]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.51.03:149][276]LogEOSSDK: LogEOS: Updating Product SDK Config, Time: 390.884644
[2025.06.01-07.51.03:453][298]LogEOSSDK: LogEOS: SDK Config Product Update Request Completed - No Change
[2025.06.01-07.51.03:454][298]LogEOSSDK: LogEOS: ScheduleNextSDKConfigDataUpdate - Time: 391.175354, Update Interval: 359.880981
[2025.06.01-07.51.11:641][886]LogBlenderLink: BlenderLink socket listener tick. ListenerSocket: valid, Active Clients: 0
[2025.06.01-07.51.13:013][924]LogWorld: UWorld::CleanupWorld for World_20, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.13:013][924]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.13:028][924]LogWorld: UWorld::CleanupWorld for World_17, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.13:028][924]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.13:059][924]LogWorld: UWorld::CleanupWorld for World_18, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.13:059][924]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.13:205][924]LogUObjectHash: Compacting FUObjectHashTables data took   1.10ms
[2025.06.01-07.51.15:008][ 67]LogUObjectHash: Compacting FUObjectHashTables data took   0.76ms
[2025.06.01-07.51.16:048][ 67]LogSlate: Window 'Save Content' being destroyed
[2025.06.01-07.51.16:129][ 67]LogStall: Shutdown...
[2025.06.01-07.51.16:129][ 67]LogStall: Shutdown complete.
[2025.06.01-07.51.16:200][ 67]LogWorld: UWorld::CleanupWorld for World_16, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:200][ 67]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:249][ 67]LogSlate: Window 'BlenderLinkProject - Unreal Editor' being destroyed
[2025.06.01-07.51.16:396][ 67]LogUObjectHash: Compacting FUObjectHashTables data took   0.86ms
[2025.06.01-07.51.16:413][ 67]Cmd: QUIT_EDITOR
[2025.06.01-07.51.16:413][ 68]LogCore: Engine exit requested (reason: UUnrealEdEngine::CloseEditor())
[2025.06.01-07.51.16:424][ 68]LogCore: Engine exit requested (reason: EngineExit() was called; note: exit was already requested)
[2025.06.01-07.51.16:424][ 68]LogStaticMesh: Abandoning remaining async distance field tasks for shutdown
[2025.06.01-07.51.16:424][ 68]LogStaticMesh: Abandoning remaining async card representation tasks for shutdown
[2025.06.01-07.51.16:448][ 68]LogWorld: UWorld::CleanupWorld for TestLevel, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:448][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:448][ 68]LogWorldPartition: UWorldPartition::Uninitialize : World = /Game/MetaHumans/Test/TestLevel.TestLevel
[2025.06.01-07.51.16:504][ 68]LogStylusInput: Shutting down StylusInput subsystem.
[2025.06.01-07.51.16:541][ 68]LogLevelSequenceEditor: LevelSequenceEditor subsystem deinitialized.
[2025.06.01-07.51.16:586][ 68]LogWorld: UWorld::CleanupWorld for World_14, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:587][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:587][ 68]LogWorld: UWorld::CleanupWorld for World_13, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:588][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:588][ 68]LogWorld: UWorld::CleanupWorld for World_12, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:588][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:588][ 68]LogWorld: UWorld::CleanupWorld for World_11, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:588][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:589][ 68]LogWorld: UWorld::CleanupWorld for World_10, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:589][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:589][ 68]LogWorld: UWorld::CleanupWorld for World_9, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:589][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:589][ 68]LogWorld: UWorld::CleanupWorld for World_8, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:589][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:589][ 68]LogWorld: UWorld::CleanupWorld for World_7, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:589][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:589][ 68]LogWorld: UWorld::CleanupWorld for World_6, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:589][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:590][ 68]LogWorld: UWorld::CleanupWorld for World_15, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:590][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:590][ 68]LogWorld: UWorld::CleanupWorld for World_4, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:590][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:590][ 68]LogWorld: UWorld::CleanupWorld for World_1, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:590][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:591][ 68]LogWorld: UWorld::CleanupWorld for World_0, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:591][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:601][ 68]LogWorld: UWorld::CleanupWorld for World_19, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:601][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:601][ 68]LogWorld: UWorld::CleanupWorld for World_21, bSessionEnded=true, bCleanupResources=true
[2025.06.01-07.51.16:601][ 68]LogSlate: InvalidateAllWidgets triggered.  All widgets were invalidated
[2025.06.01-07.51.16:613][ 68]LogStudioTelemetry: Ended StudioTelemetry Session
[2025.06.01-07.51.16:620][ 68]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroying ET Analytics provider
[2025.06.01-07.51.16:620][ 68]LogAnalytics: Display: [UEEditor.Rocket.Release] Ended ET Analytics provider session
[2025.06.01-07.51.16:620][ 68]LogAnalytics: Display: [UEEditor.Rocket.Release] Destroyed ET Analytics provider
[2025.06.01-07.51.16:655][ 68]LogAudio: Display: Beginning Audio Device Manager Shutdown (Module: AudioMixerXAudio2)...
[2025.06.01-07.51.16:655][ 68]LogAudio: Display: Destroying 1 Remaining Audio Device(s)...
[2025.06.01-07.51.16:655][ 68]LogAudio: Display: Audio Device unregistered from world 'TestLevel'.
[2025.06.01-07.51.16:655][ 68]LogAudio: Display: Shutting down audio device while 1 references to it are still alive. For more information, compile with INSTRUMENT_AUDIODEVICE_HANDLES.
[2025.06.01-07.51.16:664][ 68]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.01-07.51.16:666][ 68]LogAudioMixer: FMixerPlatformXAudio2::StopAudioStream() called. InstanceID=1
[2025.06.01-07.51.16:695][ 68]LogAudioMixer: Deinitializing Audio Bus Subsystem for audio device with ID -1
[2025.06.01-07.51.16:695][ 68]LogAudio: Display: Audio Device Manager Shutdown
[2025.06.01-07.51.16:695][ 68]LogWindowsTextInputMethodSystem: Activated input method: English (United States) - (Keyboard).
[2025.06.01-07.51.16:698][ 68]LogSlate: Slate User Destroyed.  User Index 0, Is Virtual User: 0
[2025.06.01-07.51.16:698][ 68]LogExit: Preparing to exit.
[2025.06.01-07.51.17:116][ 68]LogUObjectHash: Compacting FUObjectHashTables data took   0.82ms
[2025.06.01-07.51.17:563][ 68]LogEditorDataStorage: Deinitializing
[2025.06.01-07.51.18:535][ 68]LogDemo: Cleaned up 0 splitscreen connections, owner deletion: enabled
[2025.06.01-07.51.18:656][ 68]LogExit: Editor shut down
[2025.06.01-07.51.18:659][ 68]LogExit: Transaction tracking system shut down
[2025.06.01-07.51.20:839][ 68]LogExit: Object subsystem successfully closed.
[2025.06.01-07.51.20:952][ 68]LogShaderCompilers: Display: Shaders left to compile 0
[2025.06.01-07.51.21:662][ 68]LogMemoryProfiler: Shutdown
[2025.06.01-07.51.21:675][ 68]LogNetworkingProfiler: Shutdown
[2025.06.01-07.51.21:675][ 68]LoadingProfiler: Shutdown
[2025.06.01-07.51.21:681][ 68]LogTimingProfiler: Shutdown
[2025.06.01-07.51.21:887][ 68]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.01-07.51.21:887][ 68]LogBlenderLink: Closing listener socket
[2025.06.01-07.51.21:887][ 68]LogBlenderLink: Shutting down BlenderLink socket listener
[2025.06.01-07.51.22:879][ 68]LogChaosDD: Chaos Debug Draw Shutdown
[2025.06.01-07.51.22:969][ 68]LogHttp: Warning: [FHttpManager::Shutdown] Unbinding delegates for 1 outstanding Http Requests:
[2025.06.01-07.51.22:969][ 68]LogHttp: Warning: 	verb=[POST] url=[https://datarouter.ol.epicgames.com/datarouter/api/v1/public/data?SessionID=%7B48F16AC1-424F-A40A-A212-6AA47453434D%7D&AppID=UEEditor.Rocket.Release&AppVersion=5.5.4-40574608%2B%2B%2BUE5%2BRelease-5.5&UserID=5eff80b14364fb2f37e5468dbd2f7de6%7C0de775007ac941b984ac36d970a4fb1c%7C6ba94555-8ddf-4db5-b84c-ec5c48d27de3&AppEnvironment=datacollector-binary&UploadType=eteventstream] refs=[2] status=Processing
[2025.06.01-07.51.24:100][ 68]LogEOSSDK: FEOSSDKManager::Shutdown EOS_Shutdown Result=[EOS_Success]
[2025.06.01-07.51.24:121][ 68]LogStudioTelemetry: Display: Shutdown StudioTelemetry Module
[2025.06.01-07.51.24:121][ 68]LogNFORDenoise: NFORDenoise function shutting down
[2025.06.01-07.51.24:122][ 68]RenderDocPlugin: plugin has been unloaded.
[2025.06.01-07.51.24:176][ 68]LogXGEController: Cleaning working directory: C:/Users/<USER>/AppData/Local/Temp/UnrealXGEWorkingDir/
[2025.06.01-07.51.24:224][ 68]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetActorFactory id: 0
[2025.06.01-07.51.24:224][ 68]LogIris: FNetObjectFactoryRegistry::UnregisterFactory is unregistering factory: None name: NetSubObjectFactory id: 1
[2025.06.01-07.51.24:224][ 68]LogPakFile: Destroying PakPlatformFile
